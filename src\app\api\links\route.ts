import { NextResponse } from 'next/server';
import pool from '@/lib/db';

// GET all links with categories
export async function GET() {
  try {
    const result = await pool.query(`
      SELECT 
        l.id, l.title, l.url, l.category_id,
        c.name as category_name
      FROM kb.links l
      JOIN kb.categories c ON l.category_id = c.id
      ORDER BY c.name, l.title
    `);

    return NextResponse.json({ links: result.rows });
  } catch (error) {
    console.error('Error fetching links:', error);
    return NextResponse.json(
      { error: 'Failed to fetch links' },
      { status: 500 }
    );
  }
}

// POST new link
export async function POST(request: Request) {
  try {
    const { title, url, category_id } = await request.json();
    
    const result = await pool.query(
      `INSERT INTO kb.links (title, url, category_id)
       VALUES ($1, $2, $3)
       RETURNING id, title, url, category_id`,
      [title, url, category_id]
    );

    return NextResponse.json(result.rows[0]);
  } catch (error) {
    console.error('Error creating link:', error);
    return NextResponse.json(
      { error: 'Failed to create link' },
      { status: 500 }
    );
  }
}