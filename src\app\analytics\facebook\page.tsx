'use client';

import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

export default function FacebookAnalytics() {
  const [metrics, setMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [metricsRes, historyRes] = await Promise.all([
          fetch('/api/social-metrics'),
          fetch('/api/facebook-history')  // We'll create this endpoint next
        ]);
        
        const [metricsData, historyData] = await Promise.all([
          metricsRes.json(),
          historyRes.json()
        ]);

        setMetrics({ current: metricsData, history: historyData });
        setLoading(false);
      } catch (error) {
        console.error('Error fetching data:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return <DashboardLayout>Loading...</DashboardLayout>;
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl text-[#365F9F] mb-2">Facebook Analytics</h1>
          <p className="text-gray-600">Detailed insights into your Facebook page performance</p>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold text-[#365F9F]">Total Followers</h3>
            <p className="text-3xl mt-2">{metrics?.current?.facebook?.toLocaleString()}</p>
            <p className="text-sm text-gray-500 mt-1">
              {metrics?.current?.facebookChange > 0 ? '↑' : '↓'} 
              {Math.abs(metrics?.current?.facebookChange)}% in last 30 days
            </p>
          </div>
          {/* Add more overview cards here */}
        </div>

        {/* Followers Growth Chart */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold text-[#365F9F] mb-4">Followers Growth</h3>
          <div className="h-[400px]">
            <Line
              data={metrics?.history?.followersData || { labels: [], datasets: [] }}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'top' as const,
                  },
                  title: {
                    display: false,
                  },
                },
              }}
            />
          </div>
        </div>

        {/* Engagement Metrics */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold text-[#365F9F] mb-4">Post Engagement</h3>
          {/* We'll add engagement charts here */}
        </div>
      </div>
    </DashboardLayout>
  );
}