import { NextResponse } from 'next/server';
import pool from '@/lib/db';

// POST - Save a new TTS generation
export async function POST(request: Request) {
  try {
    const { prompt, voiceActor, audioUrl, favorite = false } = await request.json();
    
    // Validate required fields
    if (!prompt || !voiceActor || !audioUrl) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const result = await pool.query(
      `INSERT INTO fal_tts.generations (prompt, voice_actor, audio_url, favorite)
       VALUES ($1, $2, $3, $4)
       RETURNING id, prompt, voice_actor, audio_url, created_at, favorite`,
      [prompt, voiceActor, audioUrl, favorite]
    );

    return NextResponse.json(result.rows[0]);
  } catch (error) {
    console.error('Error saving TTS generation:', error);
    return NextResponse.json(
      { error: 'Failed to save TTS generation' },
      { status: 500 }
    );
  }
}

// GET - Retrieve TTS generations
export async function GET() {
  try {
    const result = await pool.query(`
      SELECT id, prompt, voice_actor, audio_url, created_at, favorite
      FROM fal_tts.generations
      ORDER BY created_at DESC
    `);

    return NextResponse.json({ generations: result.rows });
  } catch (error) {
    console.error('Error fetching TTS generations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch TTS generations' },
      { status: 500 }
    );
  }
}