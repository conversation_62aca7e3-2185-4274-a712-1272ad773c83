"use client";

import { ReactNode, useEffect, useState } from 'react';
import LoadingState from './LoadingState';

// Metric card component for individual social platform stats
interface MetricCardProps {
  title: string;
  value: string | number;
  subValue?: string;
  rightSubValue?: string;
  change?: number;
  icon: ReactNode;
  bgColor: string;
}

const MetricCard = ({ title, value, subValue, rightSubValue, change, icon, bgColor }: MetricCardProps) => {
  return (
    <div className={`${bgColor} p-5 rounded-lg shadow-md text-white flex flex-col h-full`}>
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg">{title}</h3>
        <div className="text-2xl">{icon}</div>
      </div>
      <div className="text-3xl mb-2">{value}</div>
      {subValue && (
        <div className="text-sm">
          <span>{subValue}</span>
        </div>
      )}
      {(change !== undefined || rightSubValue) && (
        <div className="flex flex-col text-sm">
          {change !== undefined && (
            <div className={`${change >= 0 ? 'text-green-300' : 'text-red-300'} flex items-center`}>
              {change >= 0 ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              )}
              {Math.abs(change)}% from last month
            </div>
          )}
          {rightSubValue && (
            <div className="text-right">
              {rightSubValue}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default function SocialMetrics() {
  const [metrics, setMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await fetch('/api/social-metrics');
        if (!response.ok) throw new Error('Failed to fetch metrics');
        const data = await response.json();
        setMetrics(data);
      } catch (err) {
        setError('Failed to load social metrics');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchMetrics();
  }, []);

  if (loading) return <LoadingState />;
  if (error) return <div>Error: {error}</div>;

  const socialMetrics = [
    {
      id: 1,
      title: 'Facebook',
      value: `${metrics?.facebook?.toLocaleString() || '0'} followers`,
      change: metrics?.facebookChange || 0,
      rightSubValue: metrics?.facebookDifference > 0 
        ? `+${metrics.facebookDifference}` 
        : metrics?.facebookDifference.toString(),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor" className="w-6 h-6">
          <path d="M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z"/>
        </svg>
      ),
      bgColor: 'bg-[#1877F2]'
    },
    {
      id: 2,
      title: 'Instagram',
      value: `${metrics?.instagram?.toLocaleString() || '0'} followers`,
      change: metrics?.instagramChange || 0,
      rightSubValue: metrics?.instagramDifference > 0 
        ? `+${metrics.instagramDifference}` 
        : metrics?.instagramDifference.toString(),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor" className="w-6 h-6">
          <path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"/>
        </svg>
      ),
      bgColor: 'bg-[#C13584]'
    },
    {
      id: 3,
      title: 'YouTube',
      value: `${metrics?.youtube?.toLocaleString() || '0'} subscribers`,
      change: metrics?.youtubeChange || 0,
      rightSubValue: metrics?.youtubeDifference > 0 
        ? `+${metrics.youtubeDifference}` 
        : metrics?.youtubeDifference.toString(),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" fill="currentColor" className="w-6 h-6">
          <path d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z"/>
        </svg>
      ),
      bgColor: 'bg-[#FF0000]'
    },
    {
      id: 4,
      title: 'Website',
      value: `${Number(metrics?.website?.visitors).toLocaleString() || '0'} visitors`,
      subValue: 'Last 30 days',
      rightSubValue: `Avg ${Math.round(Number(metrics?.website?.visitors || 0) / 30).toLocaleString()}/day`,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor" className="w-6 h-6">
          <path d="M352 256c0 22.2-1.2 43.6-3.3 64H163.3c-2.2-20.4-3.3-41.8-3.3-64s1.2-43.6 3.3-64h185.4c2.2 20.4 3.3 41.8 3.3 64zm28.8-64h123.1c5.3 20.5 8.1 41.9 8.1 64s-2.8 43.5-8.1 64H380.8c2.1-20.6 3.2-42 3.2-64s-1.1-43.4-3.2-64zm112.6-32H376.7c-10-63.9-29.8-117.4-55.3-151.6c78.3 20.7 142 77.5 171.9 151.6zm-149.1 0H167.7c6.1-36.4 15.5-68.6 27-94.7c10.5-23.6 22.2-40.7 33.5-51.5C239.4 3.2 248.7 0 256 0s16.6 3.2 27.8 13.8c11.3 10.8 23 27.9 33.5 51.5c11.6 26 20.9 58.2 27 94.7zm-209 0H18.6C48.6 85.9 112.2 29.1 190.6 8.4C165.1 42.6 145.3 96.1 135.3 160zM8.1 192H131.2c-2.1 20.6-3.2 42-3.2 64s1.1 43.4 3.2 64H8.1C2.8 299.5 0 278.1 0 256s2.8-43.5 8.1-64zM194.7 446.6c-11.6-26-20.9-58.2-27-94.6h176.6c-6.1 36.4-15.5 68.6-27 94.6c-10.5 23.6-22.2 40.7-33.5 51.5C272.6 508.8 263.3 512 256 512s-16.6-3.2-27.8-13.8c-11.3-10.8-23-27.9-33.5-51.5zM135.3 352c10 63.9 29.8 117.4 55.3 151.6C112.2 482.9 48.6 426.1 18.6 352H135.3zm358.1 0c-30 74.1-93.6 130.9-171.9 151.6c25.5-34.2 45.2-87.7 55.3-151.6H493.4z"/>
        </svg>
      ),
      bgColor: 'bg-[#F57E02]'
    },
    {
      id: 5,
      title: 'Nextdoor',
      value: `${metrics?.nextdoor?.toLocaleString() || '0'} neighbors`,
      change: metrics?.nextdoorChange || 0,
      rightSubValue: metrics?.nextdoorDifference > 0 
        ? `+${metrics.nextdoorDifference}` 
        : metrics?.nextdoorDifference.toString(),
      subValue: 'Updated monthly',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor" className="w-6 h-6">
          <path d="M480 0H32C14.3 0 0 14.3 0 32v448c0 17.7 14.3 32 32 32h448c17.7 0 32-14.3 32-32V32c0-17.7-14.3-32-32-32zM272 256c0 35.3-28.7 64-64 64s-64-28.7-64-64 28.7-64 64-64 64 28.7 64 64zM48 448c0-35.3 28.7-64 64-64h288c35.3 0 64 28.7 64 64v16H48v-16zm448-160c0 8.8-7.2 16-16 16h-32c-8.8 0-16-7.2-16-16v-32c0-8.8 7.2-16 16-16h32c8.8 0 16 7.2 16 16v32zm0-96c0 8.8-7.2 16-16 16h-32c-8.8 0-16-7.2-16-16v-32c0-8.8 7.2-16 16-16h32c8.8 0 16 7.2 16 16v32z"/>
        </svg>
      ),
      bgColor: 'bg-[#19975d]'
    },
    {
      id: 6,
      title: 'Cablecast: Channel 26',
      value: `${metrics?.cablecast26?.showCount?.toLocaleString() || '0'} aired shows`,
      subValue: 'Last 30 days',
      rightSubValue: `${metrics?.cablecast26?.totalHours || '0'} hours`,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" fill="currentColor" className="w-6 h-6">
          <path d="M336.2 64H47.8C21.4 64 0 85.4 0 111.8v288.4C0 426.6 21.4 448 47.8 448h288.4c26.4 0 47.8-21.4 47.8-47.8V111.8c0-26.4-21.4-47.8-47.8-47.8zm189.4 37.7L416 177.3v157.4l109.6 75.5c21.2 14.6 50.4-.3 50.4-25.8V127.5c0-25.4-29.1-40.4-50.4-25.8z"/>
        </svg>
      ),
      bgColor: 'bg-[#2DB566]'
    },
    {
      id: 7,
      title: 'DAMS',
      value: `${metrics?.dams?.assetCount?.toLocaleString() || '0'} assets`,
      subValue: `${metrics?.dams?.downloads?.toLocaleString() || '0'} downloads`,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
          <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      bgColor: 'bg-[#7B8ABB]'
    },
    {
      id: 8,
      title: 'Twitter/X',
      value: '1082 followers',
      subValue: '',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor" className="w-6 h-6">
          <path d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z"/>
        </svg>
      ),
      bgColor: 'bg-[#1DA1F2]'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {socialMetrics.map(metric => (
        <MetricCard
          key={metric.id}
          title={metric.title}
          value={metric.value}
          subValue={metric.subValue}
          rightSubValue={metric.rightSubValue}
          change={metric.change}
          icon={metric.icon}
          bgColor={metric.bgColor}
        />
      ))}
    </div>
  );
}
