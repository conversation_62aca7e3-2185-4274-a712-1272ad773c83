# PDF Compression Service - Authentication Guide

## 🔐 Security Implementation

Your PDF compression service is now secured with API key authentication using Bearer tokens. This ensures only authorized clients can access your compression endpoints.

## 🔑 API Key Details

### Generated API Key
```
pdf_compress_2024_a7b9c3d5e8f1g4h6j9k2m5n8p1q4r7s0t3u6v9w2x5y8z1
```

**⚠️ Important Security Notes:**
- This is a 64-character secure random key
- Store it securely and never commit it to version control
- Generate a new key for production environments
- Consider rotating keys periodically

### Configuration
The API key is configured in your `.env` file:
```env
API_KEY=pdf_compress_2024_a7b9c3d5e8f1g4h6j9k2m5n8p1q4r7s0t3u6v9w2x5y8z1
```

## 🛡️ Protected Endpoints

### Requires Authentication:
- `POST /compress` - Upload and compress PDFs
- `GET /status/{job_id}` - Get compression status
- `GET /download/{file_id}` - Download compressed files

### Public Endpoints:
- `GET /health` - Health check (no authentication required)

## 📡 How to Use Authentication

### 1. HTTP Header Method (Recommended)
Include the API key in the `Authorization` header:

```bash
Authorization: Bearer pdf_compress_2024_a7b9c3d5e8f1g4h6j9k2m5n8p1q4r7s0t3u6v9w2x5y8z1
```

### 2. Example Requests

**cURL:**
```bash
curl -X POST "http://localhost:8000/compress" \
  -H "Authorization: Bearer pdf_compress_2024_a7b9c3d5e8f1g4h6j9k2m5n8p1q4r7s0t3u6v9w2x5y8z1" \
  -F "file=@document.pdf" \
  -F "image_quality=75"
```

**JavaScript/Fetch:**
```javascript
const response = await fetch('http://localhost:8000/compress', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer pdf_compress_2024_a7b9c3d5e8f1g4h6j9k2m5n8p1q4r7s0t3u6v9w2x5y8z1'
  },
  body: formData
});
```

**Python/Requests:**
```python
import requests

headers = {
    'Authorization': 'Bearer pdf_compress_2024_a7b9c3d5e8f1g4h6j9k2m5n8p1q4r7s0t3u6v9w2x5y8z1'
}

response = requests.post(
    'http://localhost:8000/compress',
    headers=headers,
    files={'file': open('document.pdf', 'rb')}
)
```

## 🚫 Error Responses

### 401 Unauthorized - Invalid API Key
```json
{
  "detail": "Invalid API key"
}
```

### 403 Forbidden - Missing Authorization Header
```json
{
  "detail": "Not authenticated"
}
```

## 🔧 Next.js Integration

### Environment Variables
Add to your `.env.local`:
```env
NEXT_PUBLIC_PDF_API_URL=http://localhost:8000
NEXT_PUBLIC_PDF_API_KEY=pdf_compress_2024_a7b9c3d5e8f1g4h6j9k2m5n8p1q4r7s0t3u6v9w2x5y8z1
```

### React Component Usage
```typescript
const API_KEY = process.env.NEXT_PUBLIC_PDF_API_KEY;

const uploadFile = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch(`${API_URL}/compress`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${API_KEY}`
    },
    body: formData
  });
  
  return response.json();
};
```

## 🔄 Key Rotation

### Generating New Keys
You can generate new API keys using the included utility:

```python
from auth import generate_api_key

# Generate a new 64-character key
new_key = generate_api_key(64)
print(f"New API key: {new_key}")
```

### Updating Keys
1. Generate a new key
2. Update your `.env` file
3. Update your client applications
4. Restart the service
5. Update your Next.js environment variables

## 🏭 Production Considerations

### 1. Environment-Specific Keys
Use different API keys for different environments:

**Development:**
```env
API_KEY=pdf_compress_dev_[random-string]
```

**Staging:**
```env
API_KEY=pdf_compress_staging_[random-string]
```

**Production:**
```env
API_KEY=pdf_compress_prod_[random-string]
```

### 2. Key Management
- Store production keys in secure environment variable systems
- Use services like AWS Secrets Manager, Azure Key Vault, or HashiCorp Vault
- Never hardcode keys in your application code
- Implement key rotation policies

### 3. Additional Security Measures
Consider implementing:
- Rate limiting per API key
- IP whitelisting
- Request logging and monitoring
- Key expiration dates
- Multiple API keys for different clients

## 🧪 Testing Authentication

### Test Script
The included `test_service.py` script tests authentication:

```bash
python test_service.py
```

### Manual Testing
```bash
# Test without authentication (should fail)
curl http://localhost:8000/compress

# Test with wrong key (should fail)
curl -H "Authorization: Bearer wrong-key" http://localhost:8000/compress

# Test with correct key (should work)
curl -H "Authorization: Bearer pdf_compress_2024_a7b9c3d5e8f1g4h6j9k2m5n8p1q4r7s0t3u6v9w2x5y8z1" http://localhost:8000/health
```

## 📋 Security Checklist

- ✅ API key authentication implemented
- ✅ Bearer token format used
- ✅ Secure 64-character random key generated
- ✅ Environment variable configuration
- ✅ Proper error responses for auth failures
- ✅ Health endpoint remains public
- ✅ All compression endpoints protected
- ✅ Documentation updated with auth examples
- ✅ Next.js integration examples provided
- ✅ Test suite includes authentication tests

## 🔗 Related Files

- `auth.py` - Authentication module
- `main.py` - FastAPI app with auth dependencies
- `.env` - Environment configuration
- `test_service.py` - Authentication testing
- `API_DOCUMENTATION.md` - Complete API reference
- `nextjs-integration-example.tsx` - React component with auth

Your PDF compression service is now secure and ready for production deployment! 🎉
