"use client";

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import Modal from './Modal';
import SocialEmbed from './SocialEmbed';
import LoadingState from './LoadingState';

interface CampaignPost {
  title: string;
  date: string;
  yt_thumb: string;
  yt_id: string;
  yt_views: number;
  yt_likes: number;
  fb_video_views: number;
  fb_post_like: number;
  fb_post_love: number;
  fb_post_impressions: number;
  fb_post_id: string;
  ig_reach: number;
  ig_likes: number;
  ig_total_interactions: number;
  ig_id: string;
  ig_permalink: string;
  fb_permalink: string;
}

interface Pagination {
  currentPage: number;
  totalPages: number;
  totalPosts: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

const formatDate = (dateStr: string) => {
  return new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
    month: 'long',
    day: 'numeric',
    year: 'numeric',
    timeZone: process.env.NEXT_PUBLIC_TZ || 'America/Los_Angeles'
  }).format(new Date(dateStr));
};

export default function CampaignPosts() {
  const [campaignTitle, setCampaignTitle] = useState('Loading...');
  const [posts, setPosts] = useState<CampaignPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeModal, setActiveModal] = useState<'youtube' | 'facebook' | 'instagram' | null>(null);
  const [activePost, setActivePost] = useState<CampaignPost | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState<Pagination | null>(null);

  useEffect(() => {
    const fetchCampaign = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/featured-campaign?page=${currentPage}`);
        if (!response.ok) throw new Error('Failed to fetch campaign');
        const data = await response.json();
        setCampaignTitle(data.campaignTitle);
        setPosts(data.posts);
        setPagination(data.pagination);
      } catch (err) {
        setError('Failed to load campaign data');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchCampaign();
  }, [currentPage]);

  const handleNextPage = () => {
    if (pagination?.hasNextPage) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (pagination?.hasPrevPage) {
      setCurrentPage(prev => prev - 1);
    }
  };

  if (loading) return <LoadingState />;
  if (error) return <div className="bg-white p-6 rounded-lg shadow-md">Error: {error}</div>;

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl text-[#365F9F] mb-4">Featured Campaign: {campaignTitle}</h2>
      <div className="space-y-6">
        {posts.map((post, index) => (
          <div key={index} className={`${index !== 0 ? 'border-t border-gray-100 pt-6' : ''}`}>
            <div className="mb-2">
              <h3 className="text-lg text-gray-800">{post.title}</h3>
              <p className="text-sm text-gray-500">
                {formatDate(post.date)}
              </p>
            </div>
            
            <div className="flex gap-4">
              {/* Thumbnail */}
              <div className="flex-1">
                <div 
                  className="bg-gray-50 p-3 rounded-lg h-full overflow-hidden cursor-pointer transition-transform hover:scale-[1.02]"
                  onClick={() => {
                    setActiveModal('youtube');
                    setActivePost(post);
                  }}
                >
                  <div className="w-full h-full rounded-lg overflow-hidden relative group">
                    <img 
                      src={post.yt_thumb} 
                      alt={post.title}
                      className="w-full h-full object-cover scale-135"
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-200 flex items-center justify-center">
                      <svg className="w-12 h-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Metrics Grid */}
              <div className="flex gap-4 flex-[3]">
                {/* YouTube Metrics */}
                <div 
                  onClick={() => {
                    setActiveModal('youtube');
                    setActivePost(post);
                  }}
                  className="bg-red-50 p-3 rounded-lg flex-1 cursor-pointer transition-transform hover:scale-[1.02]"
                >
                  <div className="flex items-center gap-2 text-[#FF0000] mb-2">
                    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                    </svg>
                    <span className="font-medium">YouTube</span>
                  </div>
                  <div className="text-gray-700">
                    <p>{(post.yt_views || 0).toLocaleString()} views</p>
                    <p>{(post.yt_likes || 0).toLocaleString()} likes</p>
                  </div>
                </div>

                {/* Facebook Metrics */}
                <div 
                  onClick={() => {
                    if (post.fb_permalink) {
                      window.open(post.fb_permalink, '_blank');
                    }
                  }}
                  className="bg-blue-50 p-3 rounded-lg flex-1 cursor-pointer transition-transform hover:scale-[1.02]"
                >
                  <div className="flex items-center gap-2 text-[#1877F2] mb-2">
                    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                    <span className="font-medium">Facebook</span>
                  </div>
                  <div className="text-gray-700">
                    <p>{(post.fb_video_views || 0).toLocaleString()} views</p>
                    <p>{(post.fb_post_impressions || 0).toLocaleString()} impressions</p>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 text-[#1877F2]" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z" />
                        </svg>
                        <span>{(post.fb_post_like || 0).toLocaleString()}</span>
                      </div>
                      <div className="flex items-center">
                        <svg className="w-4 h-4 text-red-500" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M11.645 20.91l-.007-.003-.022-.012a15.247 15.247 0 01-.383-.218 25.18 25.18 0 01-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0112 5.052 5.5 5.5 0 0116.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 01-4.244 3.17 15.247 15.247 0 01-.383.219l-.022.012-.007.004-.003.001a.752.752 0 01-.704 0l-.003-.001z"/>
                        </svg>
                        <span>{(post.fb_post_love || 0).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Instagram Metrics */}
                <div
                  onClick={() => {
                    if (post.ig_permalink) {
                      window.open(post.ig_permalink, '_blank');
                    }
                  }}
                  className="bg-purple-50 p-3 rounded-lg flex-1 cursor-pointer transition-transform hover:scale-[1.02]"
                >
                  <div className="flex items-center gap-2 text-[#E4405F] mb-2">
                    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 0C8.74 0 8.333.015 7.053.072 5.775.132 4.905.333 4.14.63c-.789.306-1.459.717-2.126 1.384S.935 3.35.63 4.14C.333 4.905.131 5.775.072 7.053.012 8.333 0 8.74 0 12s.015 3.667.072 4.947c.06 1.277.261 2.148.558 2.913.306.788.717 1.459 1.384 2.126.667.666 1.336 1.079 2.126 1.384.766.296 1.636.499 2.913.558C8.333 23.988 8.74 24 12 24s3.667-.015 4.947-.072c1.277-.06 2.148-.262 2.913-.558.788-.306 1.459-.718 2.126-1.384.666-.667 1.079-1.335 1.384-2.126.296-.765.499-1.636.558-2.913.06-1.28.072-1.687.072-4.947s-.015-3.667-.072-4.947c-.06-1.277-.262-2.149-.558-2.913-.306-.789-.718-1.459-1.384-2.126C21.319 1.347 20.651.935 19.86.63c-.765-.297-1.636-.499-2.913-.558C15.667.012 15.26 0 12 0zm0 2.16c3.203 0 3.585.016 4.85.071 1.17.055 1.805.249 2.227.415.562.217.96.477 1.382.896.419.42.679.819.896 1.381.164.422.36 1.057.413 2.227.057 1.266.07 1.646.07 4.85s-.015 3.585-.074 4.85c-.061 1.17-.256 1.805-.421 2.227-.224.562-.479.96-.899 1.382-.419.419-.824.679-1.38.896-.42.164-1.065.36-2.235.413-1.274.057-1.649.07-4.859.07-3.211 0-3.586-.015-4.859-.074-1.171-.061-1.816-.256-2.236-.421-.569-.224-.96-.479-1.379-.899-.421-.419-.69-.824-.9-1.38-.165-.42-.359-1.065-.42-2.235-.045-1.26-.061-1.649-.061-4.844 0-3.196.016-3.586.061-4.861.061-1.17.255-1.814.42-2.234.21-.57.479-.96.9-1.381.419-.419.81-.689 1.379-.898.42-.166 1.051-.361 2.221-.421 1.275-.045 1.65-.06 4.859-.06l.045.03zm0 3.678c-3.405 0-6.162 2.76-6.162 6.162 0 3.405 2.76 6.162 6.162 6.162 3.405 0 6.162-2.76 6.162-6.162 0-3.405-2.76-6.162-6.162-6.162zM12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm7.846-10.405c0 .795-.646 1.44-1.44 1.44-.795 0-1.44-.646-1.44-1.44 0-.794.646-1.439 1.44-1.439.793-.001 1.44.645 1.44 1.439z"/>
                    </svg>
                    <span className="font-medium">Instagram</span>
                  </div>
                  <div className="text-gray-700">
                    <p>{(post.ig_reach || 0).toLocaleString()} reach</p>
                    <div className="flex items-center gap-1">
                      <svg className="w-4 h-4 text-red-500" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M11.645 20.91l-.007-.003-.022-.012a15.247 15.247 0 01-.383-.218 25.18 25.18 0 01-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0112 5.052 5.5 5.5 0 0116.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 01-4.244 3.17 15.247 15.247 0 01-.383.219l-.022.012-.007.004-.003.001a.752.752 0 01-.704 0l-.003-.001z"/>
                      </svg>
                      <span>{(post.ig_likes || 0).toLocaleString()}</span>
                    </div>
                    <p>{(post.ig_total_interactions || 0).toLocaleString()} interactions</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination Controls */}
      {pagination && pagination.totalPages > 1 && (
        <div className="mt-6 flex justify-between items-center">
          <button 
            onClick={handlePrevPage}
            disabled={!pagination.hasPrevPage}
            className={`px-4 py-2 rounded-md flex items-center gap-1 ${
              pagination.hasPrevPage 
                ? 'bg-blue-50 text-blue-600 hover:bg-blue-100' 
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            }`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Previous
          </button>
          
          <div className="text-sm text-gray-600">
            Page {pagination.currentPage} of {pagination.totalPages}
          </div>
          
          <button 
            onClick={handleNextPage}
            disabled={!pagination.hasNextPage}
            className={`px-4 py-2 rounded-md flex items-center gap-1 ${
              pagination.hasNextPage 
                ? 'bg-blue-50 text-blue-600 hover:bg-blue-100' 
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            }`}
          >
            Next
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      )}

      {activeModal && activePost && (
        <Modal 
          isOpen={activeModal !== null}
          onClose={() => {
            setActiveModal(null);
            setActivePost(null);
          }}
        >
          <SocialEmbed
            type={activeModal}
            id={activeModal === 'youtube' ? activePost.yt_id : activePost.fb_post_id}
            ig_permalink={activePost.ig_permalink}
            fb_permalink={activePost.fb_permalink}
          />
        </Modal>
      )}
    </div>
  );
}
