# PDF Compression Service

A FastAPI-based service for compressing PDF files with multiple compression methods and real-time progress tracking.

## Features

- **API Key Authentication**: Secure access with Bearer token authentication
- **Multiple Compression Methods**: PyMuPDF, Ghostscript, PDF2Image, and Auto (intelligent selection)
- **Page Skipping**: Skip specific pages from compression using comma-separated values or ranges
- **Real-time Progress**: WebSocket-like status updates during compression
- **File Management**: Automatic cleanup of old files
- **Docker Support**: Easy deployment with <PERSON>er and Docker Compose
- **CORS Enabled**: Ready for frontend integration
- **Health Checks**: Built-in health monitoring
- **Large File Support**: Up to 300MB file uploads

## Quick Start

### Using Docker (Recommended)

1. Clone the repository:
```bash
git clone <repository-url>
cd pdfcompress
```

2. Build and run with Docker Compose:
```bash
docker-compose up --build
```

3. Copy `.env.example` to `.env` and update the API key if needed

4. The service will be available at `http://localhost:8000`

### Manual Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Install system dependencies:
   - **Ubuntu/Debian**: `sudo apt-get install ghostscript poppler-utils`
   - **macOS**: `brew install ghostscript poppler`
   - **Windows**: Download and install Ghostscript from the official website

3. Run the service:
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## Authentication

All API endpoints require authentication using a Bearer token in the Authorization header:

```bash
Authorization: Bearer pdf_compress_2024_a7b9c3d5e8f1g4h6j9k2m5n8p1q4r7s0t3u6v9w2x5y8z1
```

The API key is configured in your `.env` file:
```env
API_KEY=pdf_compress_2024_a7b9c3d5e8f1g4h6j9k2m5n8p1q4r7s0t3u6v9w2x5y8z1
```

## API Endpoints

### POST /compress

Upload and compress a PDF file.

**Authentication:** Required (Bearer token)

**Parameters:**
- `file`: PDF file (multipart/form-data)
- `image_quality`: JPEG quality 1-100 (default: 75)
- `image_scale`: Scale factor for large images 0.1-1.0 (default: 0.8)
- `skip_pages`: Pages to skip from compression (optional)
- `method`: Compression method - "auto", "pymupdf", "ghostscript", "pdf2image" (default: "auto")
- `target_size_mb`: Target file size in MB (optional)

**Skip Pages Format:**
- Single pages: `"1,5,10"`
- Ranges: `"10-15"`
- Mixed: `"1,5,10-15,20"`

**Response:**
```json
{
  "job_id": "uuid-string",
  "status": "pending",
  "message": "PDF compression job started"
}
```

### GET /status/{job_id}

Get real-time status of a compression job.

**Authentication:** Required (Bearer token)

**Response:**
```json
{
  "job_id": "uuid-string",
  "status": "processing",
  "progress": 45.5,
  "current_page": 23,
  "total_pages": 50,
  "message": "Processing page 23/50",
  "download_url": null,
  "original_size_mb": 15.2,
  "compressed_size_mb": null,
  "compression_ratio": null,
  "error_details": null
}
```

**Status Values:**
- `pending`: Job created, waiting to start
- `processing`: Currently compressing
- `completed`: Compression finished successfully
- `failed`: Compression failed

### GET /download/{file_id}

Download the compressed PDF file.

**Authentication:** Required (Bearer token)

**Response:** PDF file download

### GET /health

Health check endpoint.

**Authentication:** Not required

**Response:**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "dependencies": {
    "pymupdf": "available",
    "ghostscript": "available",
    "pdf2image": "available"
  }
}
```

## Configuration

Create a `.env` file based on `.env.example`:

```env
API_KEY=pdf_compress_2024_a7b9c3d5e8f1g4h6j9k2m5n8p1q4r7s0t3u6v9w2x5y8z1
UPLOAD_DIR=uploads
OUTPUT_DIR=outputs
MAX_FILE_SIZE=300
CLEANUP_INTERVAL_HOURS=24
BASE_URL=http://localhost:8000
```

## Compression Methods

### 1. PyMuPDF (Default)
- Fast and reliable
- Preserves document structure
- Good for documents with many images
- Supports page skipping

### 2. Ghostscript
- Industry standard
- Excellent compression ratios
- Good for text-heavy documents
- Does not support page skipping

### 3. PDF2Image
- Converts to images and back
- Good for complex layouts
- Supports page skipping
- Slower but reliable

### 4. Auto (Recommended)
- Intelligently selects the best method
- Tries multiple approaches
- Optimizes for target file size
- Falls back if methods fail

## Integration with Next.js

### Example Frontend Code

```typescript
// Upload and compress PDF
async function compressPDF(file: File, options: CompressionOptions, apiKey: string) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('image_quality', options.imageQuality.toString());
  formData.append('skip_pages', options.skipPages || '');
  formData.append('method', options.method || 'auto');

  const response = await fetch('http://localhost:8000/compress', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`
    },
    body: formData,
  });

  const result = await response.json();
  return result.job_id;
}

// Poll for status updates
async function pollStatus(jobId: string, apiKey: string) {
  const response = await fetch(`http://localhost:8000/status/${jobId}`, {
    headers: {
      'Authorization': `Bearer ${apiKey}`
    }
  });
  const status = await response.json();

  if (status.status === 'completed') {
    // Download is ready
    window.open(status.download_url, '_blank');
  } else if (status.status === 'failed') {
    // Handle error
    console.error('Compression failed:', status.error_details);
  } else {
    // Update progress UI
    updateProgress(status.progress, status.message);

    // Poll again in 1 second
    setTimeout(() => pollStatus(jobId, apiKey), 1000);
  }
}
```

### React Hook Example

```typescript
import { useState, useEffect } from 'react';

interface CompressionStatus {
  jobId: string;
  status: string;
  progress: number;
  message: string;
  downloadUrl?: string;
  error?: string;
}

export function useCompression() {
  const [status, setStatus] = useState<CompressionStatus | null>(null);
  
  const compressFile = async (file: File, options: any) => {
    // Upload file
    const jobId = await compressPDF(file, options);
    
    // Start polling
    setStatus({ jobId, status: 'pending', progress: 0, message: 'Starting...' });
    pollStatus(jobId);
  };
  
  const pollStatus = async (jobId: string) => {
    try {
      const response = await fetch(`http://localhost:8000/status/${jobId}`);
      const data = await response.json();
      
      setStatus(data);
      
      if (data.status === 'processing') {
        setTimeout(() => pollStatus(jobId), 1000);
      }
    } catch (error) {
      setStatus(prev => prev ? { ...prev, status: 'failed', error: 'Network error' } : null);
    }
  };
  
  return { status, compressFile };
}
```

## Production Deployment

### Environment Variables

For production, update these environment variables:

```env
API_KEY=your-secure-api-key-here
BASE_URL=https://your-domain.com
MAX_FILE_SIZE=300
CLEANUP_INTERVAL_HOURS=12
```

### Docker Production

```bash
# Build for production
docker build -t pdf-compression-service .

# Run with production settings
docker run -d \
  -p 8000:8000 \
  -e BASE_URL=https://your-domain.com \
  -e MAX_FILE_SIZE=200 \
  -v /path/to/uploads:/app/uploads \
  -v /path/to/outputs:/app/outputs \
  pdf-compression-service
```

### Nginx Reverse Proxy

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    client_max_body_size 200M;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Troubleshooting

### Common Issues

1. **Ghostscript not found**
   - Install Ghostscript system package
   - Ensure it's in PATH

2. **Large file uploads failing**
   - Increase `MAX_FILE_SIZE` environment variable
   - Configure nginx `client_max_body_size`

3. **Out of memory errors**
   - Reduce `image_quality` parameter
   - Use smaller `image_scale` values
   - Process files in smaller batches

### Logs

Check application logs:
```bash
docker-compose logs -f pdf-compression-service
```

## License

MIT License - see LICENSE file for details.
