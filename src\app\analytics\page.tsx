import DashboardLayout from '@/components/DashboardLayout';

export default function AnalyticsPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl text-[#365F9F] mb-2">Analytics</h1>
          <p className="text-gray-600">Track and analyze social media and web performance.</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl text-[#365F9F] mb-4">Platform Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="font-bold text-[#365F9F] mb-2">Facebook Analytics</h3>
              <p className="text-gray-600">Detailed metrics for Facebook engagement coming soon.</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="font-bold text-[#365F9F] mb-2">Instagram Analytics</h3>
              <p className="text-gray-600">Detailed metrics for Instagram engagement coming soon.</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="font-bold text-[#365F9F] mb-2">YouTube Analytics</h3>
              <p className="text-gray-600">Detailed metrics for YouTube engagement coming soon.</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="font-bold text-[#365F9F] mb-2">Website Analytics</h3>
              <p className="text-gray-600">Detailed metrics for Website traffic coming soon.</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl text-[#365F9F] mb-4">Campaign Performance</h2>
          <p className="text-gray-600">Comprehensive analytics for marketing campaigns will be available here.</p>
        </div>
      </div>
    </DashboardLayout>
  );
}
