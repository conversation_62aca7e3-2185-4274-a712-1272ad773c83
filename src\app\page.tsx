import DashboardLayout from '@/components/DashboardLayout';
import SocialMetrics from '@/components/SocialMetrics';
import PopularPagesSection from '@/components/PopularPagesSection';
import CampaignPosts from '@/components/CampaignPosts';
import TopFacebookPosts from '@/components/TopFacebookPosts';
import TopInstagramPosts from '@/components/TopInstagramPosts';

export default function Home() {
  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl text-[#365F9F] mb-2">Marketing & Outreach Dashboard</h1>
          <p className="text-gray-600">Welcome to the City of Fairfield Marketing and Outreach Division portal.</p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl text-[#365F9F] mb-4">At a Glance</h2>
          <SocialMetrics />
        </div>

        <TopFacebookPosts />
        <TopInstagramPosts />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <CampaignPosts />
          <PopularPagesSection />
        </div>
      </div>
    </DashboardLayout>
  );
}











