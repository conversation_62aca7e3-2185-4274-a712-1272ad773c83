import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const days = parseInt(searchParams.get('days') || '30');

  try {
    const result = await pool.query(`
      WITH RECURSIVE DateSeries AS (
        SELECT CURRENT_DATE - INTERVAL '${days} days' as date
        UNION ALL
        SELECT date + INTERVAL '1 day'
        FROM DateSeries
        WHERE date < CURRENT_DATE
      ),
      TopPosts AS (
        SELECT DISTINCT ON (m.id) 
          m.id,
          m.caption,
          m.timestamp,
          m.thumbnail_url,
          m.permalink,
          COALESCE(mi_reach.value, 0) as reach,
          COALESCE(mi_likes.value, 0) as likes
        FROM ig.media m
        LEFT JOIN ig.media_insights mi_reach ON mi_reach.media_id = m.id 
          AND mi_reach.insight_type = 'reach'
        LEFT JOIN ig.media_insights mi_likes ON mi_likes.media_id = m.id 
          AND mi_likes.insight_type = 'likes'
        WHERE m.timestamp >= CURRENT_DATE - INTERVAL '${days} days'
        ORDER BY m.id, mi_reach.last_updated DESC
      ),
      DailyReach AS (
        SELECT 
          media_id,
          DATE(last_updated) as date,
          value as reach
        FROM ig.media_insights
        WHERE insight_type = 'reach'
          AND last_updated >= CURRENT_DATE - INTERVAL '${days} days'
          AND last_updated <= CURRENT_DATE
      )
      SELECT 
        p.*,
        (
          SELECT json_agg(
            json_build_object(
              'date', ds.date::text,
              'reach', COALESCE(dr.reach, 
                (SELECT value 
                FROM ig.media_insights 
                WHERE media_id = p.id 
                  AND insight_type = 'reach' 
                  AND DATE(last_updated) <= ds.date 
                ORDER BY last_updated DESC 
                LIMIT 1)
              )
            )
            ORDER BY ds.date
          )
          FROM DateSeries ds
          LEFT JOIN DailyReach dr ON dr.media_id = p.id AND dr.date = ds.date
        ) as daily_reach
      FROM TopPosts p
      ORDER BY p.reach DESC
      LIMIT 3
    `);

    // Log the first post's daily_reach for debugging
    if (result.rows.length > 0) {
      console.log('First post daily_reach:', result.rows[0].daily_reach);
    }

    return NextResponse.json({ posts: result.rows });
  } catch (error) {
    console.error('Error fetching top Instagram posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch top Instagram posts' },
      { status: 500 }
    );
  }
}






