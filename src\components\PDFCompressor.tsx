"use client";

import { useState, useRef, useCallback, ChangeEvent } from 'react';

interface CompressionOptions {
  imageQuality: number;
  imageScale: number;
  skipPages: string;
  method: 'auto' | 'pymupdf' | 'ghostscript' | 'pdf2image';
  targetSizeMB?: number;
}

interface CompressionStatus {
  jobId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  currentPage?: number;
  totalPages?: number;
  message: string;
  downloadUrl?: string;
  originalSizeMB?: number;
  compressedSizeMB?: number;
  compressionRatio?: number;
  errorDetails?: string;
}

export default function PDFCompressor() {
  const [file, setFile] = useState<File | null>(null);
  const [status, setStatus] = useState<CompressionStatus | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [options, setOptions] = useState<CompressionOptions>({
    imageQuality: 75,
    imageScale: 0.8,
    skipPages: '',
    method: 'auto',
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  // API configuration
  const API_BASE_URL = process.env.NEXT_PUBLIC_PDF_API_URL;
  const API_KEY = process.env.NEXT_PUBLIC_PDF_API_KEY;

  // Handle drag events for the drop zone
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    // Only set dragging to false if we're leaving the drop zone itself
    if (dropZoneRef.current && !dropZoneRef.current.contains(e.relatedTarget as Node)) {
      setIsDragging(false);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles && droppedFiles.length > 0) {
      handleFiles(droppedFiles);
    }
  };

  // Process the files from either drag-drop or file input
  const handleFiles = (fileList: FileList) => {
    if (!fileList || fileList.length === 0) return;

    const selectedFile = fileList[0]; // Only take the first file for PDF compression
    
    if (selectedFile.type === 'application/pdf') {
      setFile(selectedFile);
      setStatus(null);
      setErrorMessage(null);
    } else {
      setErrorMessage('Please select a valid PDF file');
    }
  };

  // Handle file input change
  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;

    handleFiles(e.target.files);

    // Reset the input value so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeFile = () => {
    setFile(null);
    setStatus(null);
    setErrorMessage(null);
  };

  // Poll for compression status
  const pollStatus = useCallback(async (jobId: string) => {
    if (!API_BASE_URL || !API_KEY) {
      setErrorMessage('API configuration missing');
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/status/${jobId}`, {
        headers: {
          'Authorization': `Bearer ${API_KEY}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch status');
      }

      const statusData: CompressionStatus = await response.json();
      setStatus(statusData);

      if (statusData.status === 'processing') {
        // Continue polling
        setTimeout(() => pollStatus(jobId), 1000);
      }
    } catch (error) {
      console.error('Error polling status:', error);
      setStatus(prev => prev ? {
        ...prev,
        status: 'failed',
        message: 'Failed to get status updates',
        errorDetails: 'Network error'
      } : null);
    }
  }, [API_BASE_URL, API_KEY]);

  const handleCompress = async () => {
    if (!file || !API_BASE_URL || !API_KEY) {
      setErrorMessage('Missing file or API configuration');
      return;
    }

    setIsUploading(true);
    setStatus(null);
    setErrorMessage(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('image_quality', options.imageQuality.toString());
      formData.append('image_scale', options.imageScale.toString());
      formData.append('skip_pages', options.skipPages);
      formData.append('method', options.method);
      
      if (options.targetSizeMB) {
        formData.append('target_size_mb', options.targetSizeMB.toString());
      }

      const response = await fetch(`${API_BASE_URL}/compress`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${API_KEY}`
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Upload failed');
      }

      const result = await response.json();
      
      setStatus({
        jobId: result.job_id,
        status: result.status,
        progress: 0,
        message: result.message,
      });

      // Start polling for status updates
      pollStatus(result.job_id);

    } catch (error) {
      console.error('Error uploading file:', error);
      setErrorMessage(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDownload = () => {
    if (status?.downloadUrl && API_KEY) {
      // Create a temporary link with authorization header
      const link = document.createElement('a');
      link.href = status.downloadUrl;
      link.download = `compressed_${file?.name || 'document.pdf'}`;
      
      // For external URLs, we need to handle the download differently
      // Since we can't set headers on a direct link, we'll fetch and download
      fetch(status.downloadUrl, {
        headers: {
          'Authorization': `Bearer ${API_KEY}`
        }
      })
      .then(response => response.blob())
      .then(blob => {
        const url = URL.createObjectURL(blob);
        link.href = url;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      })
      .catch(error => {
        console.error('Download failed:', error);
        setErrorMessage('Download failed. Please try again.');
      });
    }
  };

  const resetForm = () => {
    setFile(null);
    setStatus(null);
    setIsUploading(false);
    setErrorMessage(null);
  };

  return (
    <div className="space-y-6">
      {/* Error message display */}
      {errorMessage && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{errorMessage}</span>
          <button
            className="absolute top-0 bottom-0 right-0 px-4 py-3"
            onClick={() => setErrorMessage(null)}
          >
            <svg className="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <title>Close</title>
              <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
            </svg>
          </button>
        </div>
      )}

      {/* File Upload Drop Zone */}
      <div
        ref={dropZoneRef}
        className={`border-2 border-dashed ${isDragging ? 'border-[#365F9F] bg-blue-50' : 'border-gray-300'} rounded-lg p-6 text-center transition-colors cursor-pointer`}
        onClick={() => fileInputRef.current?.click()}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          className="hidden"
          accept=".pdf"
        />
        <div className="space-y-3">
          <svg xmlns="http://www.w3.org/2000/svg" className={`h-12 w-12 mx-auto ${isDragging ? 'text-[#365F9F]' : 'text-gray-400'} transition-colors`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-700">Drag PDF here or click to upload</h3>
          <p className="text-sm text-gray-500">Upload a PDF file to compress</p>
          <button
            onClick={(e) => {
              e.stopPropagation();
              fileInputRef.current?.click();
            }}
            className="mt-2 px-4 py-2 bg-[#365F9F] text-white rounded-md hover:bg-[#2A4C7F] transition-colors"
          >
            Select PDF
          </button>
        </div>
      </div>

      {/* Selected File Display */}
      {file && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-700">Selected PDF</h3>
          <div className="border rounded-lg p-4 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <div>
                  <p className="font-medium text-gray-900">{file.name}</p>
                  <p className="text-sm text-gray-500">{(file.size / (1024 * 1024)).toFixed(2)} MB</p>
                </div>
              </div>
              <button
                onClick={removeFile}
                className="text-red-600 hover:text-red-800 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Compression Options */}
      {file && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-700">Compression Options</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Image Quality (1-100)
              </label>
              <input
                type="number"
                min="1"
                max="100"
                value={options.imageQuality}
                onChange={(e) => setOptions(prev => ({ ...prev, imageQuality: parseInt(e.target.value) || 75 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#365F9F]"
              />
              <p className="text-xs text-gray-500 mt-1">Lower values = smaller file size</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Image Scale (0.1-1.0)
              </label>
              <input
                type="number"
                min="0.1"
                max="1.0"
                step="0.1"
                value={options.imageScale}
                onChange={(e) => setOptions(prev => ({ ...prev, imageScale: parseFloat(e.target.value) || 0.8 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#365F9F]"
              />
              <p className="text-xs text-gray-500 mt-1">Scale factor for large images</p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Skip Pages (optional)
            </label>
            <input
              type="text"
              value={options.skipPages}
              onChange={(e) => setOptions(prev => ({ ...prev, skipPages: e.target.value }))}
              placeholder="e.g., 1,5,10-12"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#365F9F]"
            />
            <p className="text-xs text-gray-500 mt-1">Pages to exclude from compression</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Compression Method
              </label>
              <select
                value={options.method}
                onChange={(e) => setOptions(prev => ({ ...prev, method: e.target.value as CompressionOptions['method'] }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#365F9F]"
              >
                <option value="auto">Auto (Recommended)</option>
                <option value="pymupdf">PyMuPDF</option>
                <option value="ghostscript">Ghostscript</option>
                <option value="pdf2image">PDF2Image</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Target Size (MB, optional)
              </label>
              <input
                type="number"
                min="0.1"
                step="0.1"
                value={options.targetSizeMB || ''}
                onChange={(e) => setOptions(prev => ({
                  ...prev,
                  targetSizeMB: e.target.value ? parseFloat(e.target.value) : undefined
                }))}
                placeholder="5.0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#365F9F]"
              />
              <p className="text-xs text-gray-500 mt-1">Desired output file size</p>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      {file && (
        <div className="flex gap-4">
          <button
            onClick={handleCompress}
            disabled={isUploading || (status?.status === 'processing')}
            className={`px-6 py-2 rounded-md ${
              isUploading || (status?.status === 'processing')
                ? 'bg-gray-300 cursor-not-allowed'
                : 'bg-[#365F9F] text-white hover:bg-[#2A4C7F]'
            } transition-colors`}
          >
            {isUploading ? 'Uploading...' : status?.status === 'processing' ? 'Processing...' : 'Compress PDF'}
          </button>

          <button
            onClick={resetForm}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            Reset
          </button>
        </div>
      )}

      {/* Status Display */}
      {status && (
        <div className="bg-gray-50 p-4 rounded-md">
          <h4 className="font-semibold text-gray-800 mb-2">Compression Status</h4>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Status:</span>
              <span className={`font-semibold ${
                status.status === 'completed' ? 'text-green-600' :
                status.status === 'failed' ? 'text-red-600' :
                status.status === 'processing' ? 'text-blue-600' :
                'text-yellow-600'
              }`}>
                {status.status.toUpperCase()}
              </span>
            </div>

            {status.progress > 0 && (
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Progress:</span>
                  <span>{status.progress.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-[#365F9F] h-2 rounded-full transition-all duration-300"
                    style={{ width: `${status.progress}%` }}
                  ></div>
                </div>
              </div>
            )}

            {status.currentPage && status.totalPages && (
              <div className="flex justify-between">
                <span>Page:</span>
                <span>{status.currentPage} / {status.totalPages}</span>
              </div>
            )}

            <div className="text-sm text-gray-600">
              {status.message}
            </div>

            {status.originalSizeMB && status.compressedSizeMB && (
              <div className="mt-3 p-3 bg-green-50 rounded border">
                <div className="text-sm space-y-1">
                  <div>Original: {status.originalSizeMB.toFixed(2)} MB</div>
                  <div>Compressed: {status.compressedSizeMB.toFixed(2)} MB</div>
                  <div>Savings: {status.compressionRatio?.toFixed(1)}%</div>
                </div>
              </div>
            )}

            {status.status === 'completed' && status.downloadUrl && (
              <button
                onClick={handleDownload}
                className="w-full mt-3 bg-[#276A30] text-white py-2 px-4 rounded-md hover:bg-[#1E5023] transition-colors"
              >
                Download Compressed PDF
              </button>
            )}

            {status.status === 'failed' && status.errorDetails && (
              <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                <div className="text-sm text-red-700">
                  Error: {status.errorDetails}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Information Section */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-medium text-gray-700 mb-2">About PDF Compress</h3>
        <p className="text-sm text-gray-600">
          This utility compresses PDF files to reduce their file size while maintaining quality.
          Perfect for reducing storage space and improving upload/download speeds.
        </p>
        <ul className="mt-2 text-sm text-gray-600 list-disc list-inside">
          <li>Supports various compression methods</li>
          <li>Adjustable image quality and scaling</li>
          <li>Option to skip specific pages</li>
          <li>Target file size specification</li>
          <li>Real-time compression progress</li>
          <li>Preserves PDF structure and metadata</li>
        </ul>
      </div>
    </div>
  );
}
