import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET(request: Request) {
  try {
    // Get the page from the URL query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const itemsPerPage = 5;
    const offset = (page - 1) * itemsPerPage;
    
    // Fetch campaign title
    const campaignResult = await pool.query(`
      SELECT title 
      FROM campaign.campaigns 
      WHERE highlighted = true 
      LIMIT 1
    `);
    
    // Fetch total count of posts for pagination
    const countResult = await pool.query(`
      SELECT COUNT(*) as total
      FROM campaign.posts
      WHERE campaign_id = 3
    `);
    
    // Fetch paginated posts
    const postsResult = await pool.query(`
      SELECT 
        title, date, yt_thumb, yt_id, yt_views, yt_likes,
        fb_video_views, fb_post_like, fb_post_love, fb_post_impressions, fb_post_id,
        ig_reach, ig_likes, ig_total_interactions, ig_id, ig_permalink, fb_permalink
      FROM campaign.posts
      WHERE campaign_id = 3
      ORDER BY date DESC
      LIMIT $1 OFFSET $2
    `, [itemsPerPage, offset]);

    const totalPosts = parseInt(countResult.rows[0].total, 10);
    const totalPages = Math.ceil(totalPosts / itemsPerPage);

    return NextResponse.json({
      campaignTitle: campaignResult.rows[0]?.title || 'No Campaign Selected',
      posts: postsResult.rows,
      pagination: {
        currentPage: page,
        totalPages: totalPages,
        totalPosts: totalPosts,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching campaign data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch campaign data' }, 
      { status: 500 }
    );
  }
}