"use client";

interface SocialEmbedProps {
  type: 'youtube' | 'facebook' | 'instagram';
  id: string;
  ig_permalink: string;
  fb_permalink: string;
}

export default function SocialEmbed({ type, id, ig_permalink, fb_permalink }: SocialEmbedProps) {
  switch (type) {
    case 'youtube':
      return (
        <div className="aspect-video">
          <iframe
            width="100%"
            height="100%"
            src={`https://www.youtube.com/embed/${id}`}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            className="rounded-lg"
          />
        </div>
      );
    case 'facebook':
      return (
        <div className="fb-post" 
          data-href={`https://www.facebook.com/cityoffairfield/posts/${id}`}
          data-width="100%"
        />
      );
    case 'instagram':
      return (
        <iframe
          src={`${ig_permalink}embed`}
          width="100%"
          height="450"
          frameBorder="0"
          scrolling="no"
          allowTransparency={true}
          className="rounded-lg"
        />
      );
    default:
      return null;
  }
}










