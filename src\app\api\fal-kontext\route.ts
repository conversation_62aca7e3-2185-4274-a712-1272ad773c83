import { NextResponse } from 'next/server';
import pool from '@/lib/db';

// GET - Fetch all Kontext generations
export async function GET() {
  try {
    const result = await pool.query(
      `SELECT id, prompt, original_image_url, result_image_url, created_at, favorite
       FROM fal_kontext.generations
       ORDER BY created_at DESC`
    );
    
    return NextResponse.json({ generations: result.rows });
  } catch (error) {
    console.error('Error fetching Kontext generations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch generations' },
      { status: 500 }
    );
  }
}

// POST - Save a new Kontext generation
export async function POST(request: Request) {
  try {
    const { prompt, originalImageUrl, resultImageUrl, favorite = false } = await request.json();
    
    // Validate required fields
    if (!prompt || !originalImageUrl || !resultImageUrl) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const result = await pool.query(
      `INSERT INTO fal_kontext.generations (prompt, original_image_url, result_image_url, favorite)
       VALUES ($1, $2, $3, $4)
       RETURNING id, prompt, original_image_url, result_image_url, created_at, favorite`,
      [prompt, originalImageUrl, resultImageUrl, favorite]
    );
    
    return NextResponse.json({ generation: result.rows[0] });
  } catch (error) {
    console.error('Error saving Kontext generation:', error);
    return NextResponse.json(
      { error: 'Failed to save generation' },
      { status: 500 }
    );
  }
}