import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const days = parseInt(searchParams.get('days') || '30');

  try {
    const result = await pool.query(`
      SELECT 
        "pagePathPlusQueryString" AS page_path,
        SUM("screenPageViews") AS total_views
      FROM 
        ga4.pages
      WHERE 
        "date"::date >= CURRENT_DATE - INTERVAL '${days} days'
        AND "date"::date <= CURRENT_DATE
      GROUP BY 
        "pagePathPlusQueryString"
      ORDER BY 
        total_views DESC
      LIMIT 100
    `);

    return NextResponse.json({ pages: result.rows });
  } catch (error) {
    console.error('Error fetching popular pages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch popular pages' }, 
      { status: 500 }
    );
  }
}

