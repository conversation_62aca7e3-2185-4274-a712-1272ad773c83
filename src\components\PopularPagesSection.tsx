"use client";

import { useState, useEffect } from 'react';
import LoadingState from './LoadingState';

interface PopularPage {
  page_path: string;
  total_views: number;
}

export default function PopularPagesSection() {
  const ITEMS_PER_PAGE = 20;
  const [pages, setPages] = useState<PopularPage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [daysBack, setDaysBack] = useState(30);

  const fetchPages = async (days: number) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/popular-pages?days=${days}`);
      if (!response.ok) throw new Error('Failed to fetch pages');
      const data = await response.json();
      setPages(data.pages);
    } catch (err) {
      setError('Failed to load popular pages');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPages(daysBack);
  }, [daysBack]);

  if (loading) return <LoadingState />;
  if (error) return <div className="bg-white p-6 rounded-lg shadow-md">Error: {error}</div>;

  const totalPages = Math.ceil(pages.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const currentPages = pages.slice(startIndex, endIndex);

  const timeRangeButtons = [30, 14, 7, 3].map((days) => (
    <button
      key={days}
      onClick={() => {
        setDaysBack(days);
        setCurrentPage(1);
      }}
      className={`px-3 py-1 text-sm rounded-md transition-colors ${
        daysBack === days
          ? 'bg-[#365F9F] text-white'
          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
      }`}
    >
      {days}d
    </button>
  ));

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl text-[#365F9F]">
          Popular Website Pages - Last {daysBack} Days
        </h2>
        <div className="flex gap-2">
          {timeRangeButtons}
        </div>
      </div>
      <div className="space-y-2">
        {currentPages.map((page, index) => (
          <div 
            key={index} 
            className="flex items-center justify-between py-2 hover:bg-gray-50 rounded-md px-2"
          >
            <div className="flex items-center space-x-3">
              <span className="text-sm font-medium text-gray-500 w-6">
                {startIndex + index + 1}.
              </span>
              <a
                href={`https://www.fairfield.ca.gov${page.page_path}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-blue-600 hover:text-blue-800 hover:underline truncate max-w-[500px]"
                title={page.page_path === '/' ? 'Home Page' : page.page_path}
              >
                {page.page_path === '/' ? 'Home Page' : page.page_path}
              </a>
            </div>
            <span className="text-sm text-gray-600 font-medium">
              {page.total_views.toLocaleString()} views
            </span>
          </div>
        ))}
      </div>

      {totalPages > 1 && (
        <div className="mt-6 flex items-center justify-center space-x-4">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className={`px-3 py-1 rounded ${
              currentPage === 1 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
            }`}
          >
            Previous
          </button>
          
          <span className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </span>
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className={`px-3 py-1 rounded ${
              currentPage === totalPages 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
            }`}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
}


