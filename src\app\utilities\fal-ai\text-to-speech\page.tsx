'use client';

import { useState, useRef, useEffect } from 'react';
import { fal } from '@fal-ai/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Volume2, Download, Pause, History } from 'lucide-react';
import { toast } from "sonner";
import Link from 'next/link';

// Define the expected response type from the TTS API
interface TTSResponse {
  audio: {
    url: string;
    content_type: string;
    file_name: string;
    file_size: number;
    duration: number;
  };
  [key: string]: unknown; // Allow for additional properties
}

// Add this interface for voice options
interface VoiceOption {
  name: string;
  value: string;
}

export default function TextToSpeechPage() {
  const [text, setText] = useState('');
  const [wordCount, setWordCount] = useState(0);
  const [selectedVoice, setSelectedVoice] = useState('Jennifer (English (US)/American)');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Voice options available in fal-ai
  const voiceOptions: VoiceOption[] = [
    // English (US/American)
    { name: 'Jennifer (American)', value: 'Jennifer (English (US)/American)' },
    { name: 'Dexter (American)', value: 'Dexter (English (US)/American)' },
    
    // English (Australian)
    { name: 'Ava (Australian)', value: 'Ava (English (AU)/Australian)' },
    { name: 'Tilly (Australian)', value: 'Tilly (English (AU)/Australian)' },
    
    // English (Canadian)
    { name: 'Charlotte (Advertising)', value: 'Charlotte (Advertising) (English (CA)/Canadian)' },
    { name: 'Charlotte (Meditation)', value: 'Charlotte (Meditation) (English (CA)/Canadian)' },
    
    // English (British)
    { name: 'Cecil (British)', value: 'Cecil (English (GB)/British)' },
    { name: 'Sterling (British)', value: 'Sterling (English (GB)/British)' },
    
    // English (Irish)
    { name: 'Cillian (Irish)', value: 'Cillian (English (IE)/Irish)' },
    { name: 'Madison (Irish)', value: 'Madison (English (IE)/Irish)' },
    
    // Other English variants
    { name: 'Ada (South African)', value: 'Ada (English (ZA)/South african)' },
    { name: 'Furio (Italian English)', value: 'Furio (English (IT)/Italian)' },
    { name: 'Alessandro (Italian English)', value: 'Alessandro (English (IT)/Italian)' },
    { name: 'Carmen (Mexican English)', value: 'Carmen (English (MX)/Mexican)' },
    { name: 'Sumita (Indian English)', value: 'Sumita (English (IN)/Indian)' },
    { name: 'Navya (Indian English)', value: 'Navya (English (IN)/Indian)' },
    { name: 'Baptiste (French English)', value: 'Baptiste (English (FR)/French)' },
    { name: 'Lumi (Finnish English)', value: 'Lumi (English (FI)/Finnish)' },
    
    // Non-English voices
    { name: 'Ronel (Afrikaans - Conversational)', value: 'Ronel Conversational (Afrikaans/South african)' },
    { name: 'Ronel (Afrikaans - Narrative)', value: 'Ronel Narrative (Afrikaans/South african)' },
    { name: 'Abdo (Arabic - Conversational)', value: 'Abdo Conversational (Arabic/Arabic)' },
    { name: 'Abdo (Arabic - Narrative)', value: 'Abdo Narrative (Arabic/Arabic)' },
    { name: 'Caroline (Portuguese - Conversational)', value: 'Caroline Conversational (Portuguese (BR)/Brazilian)' },
    { name: 'Caroline (Portuguese - Narrative)', value: 'Caroline Narrative (Portuguese (BR)/Brazilian)' },
    { name: 'Ange (French - Conversational)', value: 'Ange Conversational (French/French)' },
    { name: 'Ange (French - Narrative)', value: 'Ange Narrative (French/French)' },
    { name: 'Anke (German - Conversational)', value: 'Anke Conversational (German/German)' },
    { name: 'Anke (German - Narrative)', value: 'Anke Narrative (German/German)' },
    { name: 'Alessandro (Italian - Conversational)', value: 'Alessandro Conversational (Italian/Italian)' },
    { name: 'Alessandro (Italian - Narrative)', value: 'Alessandro Narrative (Italian/Italian)' },
    { name: 'Carmen (Spanish - Conversational)', value: 'Carmen Conversational (Spanish/Spanish)' },
    { name: 'Patricia (Spanish - Conversational)', value: 'Patricia Conversational (Spanish/Spanish)' },
  ];

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    setText(newText);
    // Count words (splitting by whitespace and filtering out empty strings)
    const words = newText.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
  };

  const isOverLimit = wordCount > 100;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!text.trim() || isOverLimit) return;

    // Stop any currently playing audio
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
    }

    setIsLoading(true);
    setError(null);

    try {
      // Initialize fal client with your API key from environment variables
      const apiKey = process.env.NEXT_PUBLIC_FAL_KEY;
      if (!apiKey) {
        throw new Error('Fal.ai API key is not configured');
      }
      
      console.log('Initializing fal.ai client with key:', apiKey ? '***' + apiKey.slice(-4) : 'not set');
      
      // Configure fal client with API key
      fal.config({
        credentials: apiKey
      });
      
      // Set a global timeout for fetch requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 seconds timeout

      console.log('Sending TTS request with text:', text.substring(0, 50) + (text.length > 50 ? '...' : ''));
      
      console.log('Sending request to fal.ai...');
      
      // Make the API call with detailed logging
      const result = await fal.subscribe('fal-ai/playai/tts/dialog', {
        input: {
          input: text,
          voices: [
            {
              voice: selectedVoice, // Use the selected voice from dropdown
              turn_prefix: 'SPEAKER: '
            }
          ],
          response_format: 'url'
        },
        logs: true,
        onQueueUpdate: (update) => {
          console.log('Queue update:', update.status);
          if (update.status === 'IN_PROGRESS' && update.logs) {
            update.logs.forEach(log => console.log('Queue log:', log.message));
          }
        }
      }).catch(err => {
        console.error('Fal.ai API error:', err);
        if (err.status) console.error('Status:', err.status);
        if (err.message) console.error('Message:', err.message);
        if (err.response) console.error('Response:', err.response);
        throw new Error(`Failed to generate speech: ${err.message || 'Unknown error'}`);
      }) as unknown as TTSResponse;

      console.log('Raw API response:', JSON.stringify(result, null, 2));

      // The response might be nested under data property
      const responseData = (result as any).data || result;
      console.log('Processed response data:', responseData);

      const audioUrl = (responseData as any)?.audio?.url || (responseData as any)?.url;
      if (!audioUrl) {
        console.error('No audio URL in response:', responseData);
        throw new Error('No audio URL received in the API response');
      }

      // Store the audio URL in state
      setAudioUrl(audioUrl);

      if (audioRef.current) {
        console.log('Setting audio source to:', audioUrl);
        audioRef.current.src = audioUrl;
        await audioRef.current.play().catch(err => {
          console.error('Error playing audio:', err);
          throw new Error('Failed to play audio. Your browser may block autoplay.');
        });
        setIsPlaying(true);
      }

      // Save the generation to the database
      try {
        const saveResponse = await fetch('/api/fal-tts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: text,
            voiceActor: selectedVoice,
            audioUrl: audioUrl,
            favorite: false
          }),
        });

        if (!saveResponse.ok) {
          console.error('Failed to save generation:', await saveResponse.text());
          toast.error("Audio was generated but couldn't be saved to history.");
        } else {
          toast.success("Audio generated and saved to history.");
        }
      } catch (saveError) {
        console.error('Error saving generation:', saveError);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('Error in TTS generation:', {
        error: err,
        message: errorMessage,
        timestamp: new Date().toISOString()
      });
      setError(`Error: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const audioElement = audioRef.current;
    const handleEnded = () => setIsPlaying(false);
    
    if (audioElement) {
      audioElement.addEventListener('ended', handleEnded);
    }
    
    return () => {
      if (audioElement) {
        audioElement.removeEventListener('ended', handleEnded);
      }
    };
  }, []);

  const toggleAudio = async () => {
    if (!audioRef.current) return;
    
    if (isPlaying) {
      // Stop audio if it's currently playing
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
    } else {
      // Play audio
      try {
        await audioRef.current.play();
        setIsPlaying(true);
      } catch (err) {
        console.error('Error playing audio:', err);
        setError('Failed to play audio. Your browser may block autoplay.');
        setIsPlaying(false);
      }
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl text-[#365F9F] mb-2">Text to Speech</h1>
        <p className="text-gray-600">Convert your text into natural-sounding speech</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Generate Speech</CardTitle>
          <CardDescription>
            Enter your text below and click the button to generate speech.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="text">Text to convert to speech</Label>
              <Textarea
                id="text"
                value={text}
                onChange={handleTextChange}
                placeholder="Type or paste your text here..."
                className={`min-h-[120px] ${isOverLimit ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                disabled={isLoading}
                required
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span className={isOverLimit ? 'text-red-500 font-medium' : ''}>
                  {wordCount} / 100 words
                </span>
                {isOverLimit && (
                  <span className="text-red-500 font-medium">
                    Maximum 100 words allowed
                  </span>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="voice">Voice</Label>
              <select
                id="voice"
                value={selectedVoice}
                onChange={(e) => setSelectedVoice(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[#365F9F] focus:border-[#365F9F]"
                disabled={isLoading}
              >
                <optgroup label="English (US/American)">
                  <option value="Jennifer (English (US)/American)">Jennifer (American)</option>
                  <option value="Dexter (English (US)/American)">Dexter (American)</option>
                </optgroup>
                
                <optgroup label="English (Australian)">
                  <option value="Ava (English (AU)/Australian)">Ava (Australian)</option>
                  <option value="Tilly (English (AU)/Australian)">Tilly (Australian)</option>
                </optgroup>
                
                <optgroup label="English (Canadian)">
                  <option value="Charlotte (Advertising) (English (CA)/Canadian)">Charlotte (Advertising)</option>
                  <option value="Charlotte (Meditation) (English (CA)/Canadian)">Charlotte (Meditation)</option>
                </optgroup>
                
                <optgroup label="English (British)">
                  <option value="Cecil (English (GB)/British)">Cecil (British)</option>
                  <option value="Sterling (English (GB)/British)">Sterling (British)</option>
                </optgroup>
                
                <optgroup label="English (Irish)">
                  <option value="Cillian (English (IE)/Irish)">Cillian (Irish)</option>
                  <option value="Madison (English (IE)/Irish)">Madison (Irish)</option>
                </optgroup>
                
                <optgroup label="Other English variants">
                  <option value="Ada (English (ZA)/South african)">Ada (South African)</option>
                  <option value="Furio (English (IT)/Italian)">Furio (Italian English)</option>
                  <option value="Alessandro (English (IT)/Italian)">Alessandro (Italian English)</option>
                  <option value="Carmen (English (MX)/Mexican)">Carmen (Mexican English)</option>
                  <option value="Sumita (English (IN)/Indian)">Sumita (Indian English)</option>
                  <option value="Navya (English (IN)/Indian)">Navya (Indian English)</option>
                  <option value="Baptiste (English (FR)/French)">Baptiste (French English)</option>
                  <option value="Lumi (English (FI)/Finnish)">Lumi (Finnish English)</option>
                </optgroup>
                
                <optgroup label="Afrikaans">
                  <option value="Ronel Conversational (Afrikaans/South african)">Ronel (Conversational)</option>
                  <option value="Ronel Narrative (Afrikaans/South african)">Ronel (Narrative)</option>
                </optgroup>
                
                <optgroup label="Arabic">
                  <option value="Abdo Conversational (Arabic/Arabic)">Abdo (Conversational)</option>
                  <option value="Abdo Narrative (Arabic/Arabic)">Abdo (Narrative)</option>
                </optgroup>
                
                <optgroup label="Bengali">
                  <option value="Mousmi Conversational (Bengali/Bengali)">Mousmi (Conversational)</option>
                  <option value="Mousmi Narrative (Bengali/Bengali)">Mousmi (Narrative)</option>
                </optgroup>
                
                <optgroup label="Portuguese">
                  <option value="Caroline Conversational (Portuguese (BR)/Brazilian)">Caroline (Conversational)</option>
                  <option value="Caroline Narrative (Portuguese (BR)/Brazilian)">Caroline (Narrative)</option>
                </optgroup>
                
                <optgroup label="French">
                  <option value="Ange Conversational (French/French)">Ange (Conversational)</option>
                  <option value="Ange Narrative (French/French)">Ange (Narrative)</option>
                </optgroup>
                
                <optgroup label="German">
                  <option value="Anke Conversational (German/German)">Anke (Conversational)</option>
                  <option value="Anke Narrative (German/German)">Anke (Narrative)</option>
                </optgroup>
                
                <optgroup label="Greek">
                  <option value="Bora Conversational (Greek/Greek)">Bora (Conversational)</option>
                  <option value="Bora Narrative (Greek/Greek)">Bora (Narrative)</option>
                </optgroup>
                
                <optgroup label="Hindi">
                  <option value="Anuj Conversational (Hindi/Indian)">Anuj (Conversational)</option>
                  <option value="Anuj Narrative (Hindi/Indian)">Anuj (Narrative)</option>
                </optgroup>
                
                <optgroup label="Italian">
                  <option value="Alessandro Conversational (Italian/Italian)">Alessandro (Conversational)</option>
                  <option value="Alessandro Narrative (Italian/Italian)">Alessandro (Narrative)</option>
                </optgroup>
                
                <optgroup label="Japanese">
                  <option value="Kiriko Conversational (Japanese/Japanese)">Kiriko (Conversational)</option>
                  <option value="Kiriko Narrative (Japanese/Japanese)">Kiriko (Narrative)</option>
                </optgroup>
                
                <optgroup label="Korean">
                  <option value="Dohee Conversational (Korean/Korean)">Dohee (Conversational)</option>
                  <option value="Dohee Narrative (Korean/Korean)">Dohee (Narrative)</option>
                </optgroup>
                
                <optgroup label="Spanish">
                  <option value="Carmen Conversational (Spanish/Spanish)">Carmen (Conversational)</option>
                  <option value="Patricia Conversational (Spanish/Spanish)">Patricia (Conversational)</option>
                </optgroup>
                
                <optgroup label="Hebrew">
                  <option value="Mary Conversational (Hebrew/Israeli)">Mary (Conversational)</option>
                  <option value="Mary Narrative (Hebrew/Israeli)">Mary (Narrative)</option>
                </optgroup>
              </select>
            </div>

            <div className="flex items-center gap-2">
              <Button 
                type="submit" 
                disabled={isLoading || !text.trim() || isOverLimit}
                className={isOverLimit ? 'opacity-70' : ''}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Volume2 className="mr-2 h-4 w-4" />
                    Generate Speech
                  </>
                )}
              </Button>
              
              {audioRef.current?.src && (
                <>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={toggleAudio}
                    disabled={isLoading || !audioUrl}
                    className={isPlaying ? "bg-gray-100" : ""}
                  >
                    {isPlaying ? (
                      <>
                        <Pause className="mr-2 h-4 w-4" />
                        Stop
                      </>
                    ) : (
                      <>
                        <Volume2 className="mr-2 h-4 w-4" />
                        Play Again
                      </>
                    )}
                  </Button>
                  
                  <Button
                    type="button"
                    variant="outline"
                    onClick={async () => {
                      if (audioUrl) {
                        try {
                          // Fetch the audio file as a blob
                          const response = await fetch(audioUrl);
                          const blob = await response.blob();
                          
                          // Create a blob URL
                          const blobUrl = window.URL.createObjectURL(blob);
                          
                          // Create a link element
                          const link = document.createElement('a');
                          link.href = blobUrl;
                          link.download = `speech-${new Date().getTime()}.mp3`;
                          
                          // Append to body, click, and clean up
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                          
                          // Release the blob URL
                          window.URL.revokeObjectURL(blobUrl);
                        } catch (err) {
                          console.error('Error downloading audio:', err);
                          setError('Failed to download audio file');
                        }
                      }
                    }}
                    disabled={isLoading || !audioUrl}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </Button>
                </>
              )}
            </div>

            {error && (
              <div className="text-red-500 text-sm mt-2">{error}</div>
            )}
          </form>

          <audio ref={audioRef} className="hidden" controls />
        </CardContent>
      </Card>

      <div className="flex justify-end mt-8">
        <Link href="/utilities/fal-ai/text-to-speech/history">
          <Button className="gap-3 text-base font-medium px-8 py-6 h-14" size="lg" variant="default">
            <History className="h-5 w-5" />
            View History
          </Button>
        </Link>
      </div>

      <div className="text-sm text-gray-500">
        
      </div>
    </div>
  );
}
