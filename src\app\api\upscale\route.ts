import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import cloudinary from '@/lib/cloudinary';
import { Readable } from 'stream';
import sharp from 'sharp';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const image = formData.get('image') as File;

    if (!image) {
      return NextResponse.json(
        { error: 'No image provided' },
        { status: 400 }
      );
    }

    // Check file size (limit to 10MB)
    if (image.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'Image size exceeds 10MB limit' },
        { status: 400 }
      );
    }

    // Check file type
    const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!validTypes.includes(image.type)) {
      return NextResponse.json(
        { error: 'Invalid image format. Supported formats: JPG, PNG, WebP' },
        { status: 400 }
      );
    }

    // Read the image as buffer to check dimensions
    const bytes = await image.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Get image dimensions
    const metadata = await sharp(buffer).metadata();
    const { width, height } = metadata;

    if (!width || !height) {
      return NextResponse.json(
        { error: 'Could not determine image dimensions' },
        { status: 400 }
      );
    }

    // Check if image is already high resolution (over 3000px in either dimension)
    const MAX_DIMENSION = 3000;
    if (width > MAX_DIMENSION || height > MAX_DIMENSION) {
      return NextResponse.json(
        {
          error: 'Image resolution too high',
          message: `Image dimensions (${width}x${height}) exceed the maximum allowed (${MAX_DIMENSION}px). This tool is designed for upscaling lower resolution images.`
        },
        { status: 400 }
      );
    }

    // Create a unique ID for this image
    const imageId = uuidv4();

    try {
      // Upload the image to Cloudinary
      const uploadResult = await new Promise<any>((resolve, reject) => {
        // Create a readable stream from the buffer
        const readableStream = new Readable();
        readableStream.push(buffer);
        readableStream.push(null);

        // Create an upload stream to Cloudinary
        const uploadStream = cloudinary.uploader.upload_stream(
          {
            folder: 'upscaler',
            resource_type: 'image',
            public_id: `original_${imageId}`,
          },
          (error, result) => {
            if (error) {
              console.error('Cloudinary upload error:', error);
              return reject(error);
            }
            resolve(result);
          }
        );

        // Pipe the readable stream to the upload stream
        readableStream.pipe(uploadStream);
      });

      // Get the original dimensions from Cloudinary response
      const { width, height } = uploadResult;

      if (!width || !height) {
        return NextResponse.json(
          { error: 'Could not determine image dimensions' },
          { status: 400 }
        );
      }

      // Get the original file format
      const format = uploadResult.format || 'jpg';

      // Create a transformation URL for AI-powered upscaling
      // We'll use a multi-step transformation for best quality
      const upscaledUrl = cloudinary.url(uploadResult.public_id, {
        secure: true, // Use HTTPS
        transformation: [
          // First, apply AI upscaling with enhancement
          {
            effect: 'upscale:400', // Enhanced upscaling
          },
          // Then, ensure exactly 2x dimensions with high quality
          {
            width: width * 2,
            height: height * 2,
            crop: 'scale', // Ensure exact dimensions
            quality: 'auto:best', // Best quality
            // Preserve the original format
            fetch_format: format,
          },
          // Finally, add some sharpening for crisp details
          {
            effect: 'sharpen:50',
          }
        ]
      });

      console.log('Generated Cloudinary URL:', upscaledUrl);

      // Return the processed image URL and metadata
      return NextResponse.json({
        success: true,
        id: imageId,
        url: upscaledUrl,
        originalSize: {
          width,
          height,
        },
        newSize: {
          width: width * 2,
          height: height * 2,
        },
        // Include the original image URL for reference
        originalUrl: uploadResult.url,
        // Include format information for download
        format: format,
        originalFilename: image.name,
      });
    } catch (cloudinaryError) {
      console.error('Cloudinary processing error:', cloudinaryError);
      return NextResponse.json(
        { error: 'Failed to process image with Cloudinary' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error processing image:', error);
    return NextResponse.json(
      { error: 'Failed to process image' },
      { status: 500 }
    );
  }
}


