"use client";

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import LoadingState from './LoadingState';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface DailyReach {
  date: string;
  reach: number;
}

interface InstagramPost {
  id: string;
  caption: string;
  timestamp: string;
  thumbnail_url: string;
  permalink: string;
  reach: number;
  likes: number;
  daily_reach: DailyReach[];
}

const ReachGraph = ({ data }: { data: DailyReach[] }) => {
  if (!data || data.length === 0) {
    return <div className="h-40 mt-4 flex items-center justify-center text-gray-500">No reach data available</div>;
  }

  // Sort data by date to ensure proper progression
  const sortedData = [...data].sort((a, b) => 
    new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  const chartData = {
    labels: sortedData.map(d => format(new Date(d.date), 'MMM d')),
    datasets: [
      {
        label: 'Reach',
        data: sortedData.map(d => d.reach),
        fill: false,
        borderColor: '#E1306C',
        tension: 0.1,
        pointRadius: 2,
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: (context: any) => `${context.parsed.y.toLocaleString()} reach`
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(this: any, tickValue: number | string) {
            if (typeof tickValue === 'number') {
              return tickValue.toLocaleString();
            }
            return tickValue;
          }
        }
      }
    }
  };

  return (
    <div className="h-40 mt-4">
      <Line data={chartData} options={options} />
    </div>
  );
};

export default function TopInstagramPosts() {
  const [posts, setPosts] = useState<InstagramPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [daysBack, setDaysBack] = useState(30); // default to 30 days

  const fetchPosts = async (days: number) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/top-instagram-posts?days=${days}`);
      if (!response.ok) throw new Error('Failed to fetch posts');
      const data = await response.json();
      setPosts(data.posts);
    } catch (err) {
      setError('Failed to load top Instagram posts');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPosts(daysBack);
  }, [daysBack]);

  if (loading) return <LoadingState />;
  if (error) return <div className="text-red-500">{error}</div>;

  const timeRangeButtons = [30, 14, 7, 3].map((days) => (
    <button
      key={days}
      onClick={() => setDaysBack(days)}
      className={`px-3 py-1 text-sm rounded-md transition-colors ${
        daysBack === days
          ? 'bg-[#E1306C] text-white'
          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
      }`}
    >
      {days}d
    </button>
  ));

  return (
    <div className="bg-white p-6 rounded-lg shadow-md mt-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl text-[#E1306C]">
          Top 3 Instagram Posts - Last {daysBack} Days
        </h2>
        <div className="flex gap-2">
          {timeRangeButtons}
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {posts.map((post) => (
          <div key={post.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            {post.thumbnail_url && (
              <div className="aspect-square relative overflow-hidden">
                <img
                  src={post.thumbnail_url}
                  alt=""
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            <div className="p-4">
              <p className="text-gray-600 text-sm mb-2">
                {format(new Date(post.timestamp), 'MMM d, yyyy')}
              </p>
              <p className="text-gray-800 line-clamp-3">{post.caption}</p>
              <div className="mt-3 pt-3 border-t border-gray-100 space-y-2">
                <div className="inline-block bg-[#E1306C] text-white px-3 py-1 rounded-full text-sm font-medium">
                  {post.reach.toLocaleString()} reach
                </div>
                <div className="flex items-center gap-1">
                  <svg className="w-5 h-5 text-red-500" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M11.645 20.91l-.007-.003-.022-.012a15.247 15.247 0 01-.383-.218 25.18 25.18 0 01-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0112 5.052 5.5 5.5 0 0116.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 01-4.244 3.17 15.247 15.247 0 01-.383.219l-.022.012-.007.004-.003.001a.752.752 0 01-.704 0l-.003-.001z"/>
                  </svg>
                  <span className="text-sm text-gray-700">{post.likes.toLocaleString()}</span>
                </div>
              </div>
              <div className="mt-4 pt-4 border-t border-gray-100">
                <ReachGraph data={post.daily_reach} />
              </div>
              <a
                href={post.permalink}
                target="_blank"
                rel="noopener noreferrer"
                className="mt-3 text-sm text-[#E1306C] hover:text-[#B2254F] font-medium block"
              >
                View on Instagram →
              </a>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

