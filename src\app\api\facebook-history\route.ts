import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET() {
  try {
    const result = await pool.query(`
      WITH daily_metrics AS (
        SELECT 
          DATE(captured_at) as date,
          followers_count,
          LAG(followers_count) OVER (ORDER BY captured_at) as prev_followers
        FROM fb.page_history
        WHERE captured_at >= NOW() - INTERVAL '30 days'
        ORDER BY captured_at
      )
      SELECT 
        date,
        followers_count,
        CASE 
          WHEN prev_followers IS NOT NULL 
          THEN followers_count - prev_followers 
          ELSE 0 
        END as daily_change
      FROM daily_metrics
      ORDER BY date;
    `);

    // Format data for Chart.js
    const followersData = {
      labels: result.rows.map(row => row.date),
      datasets: [
        {
          label: 'Total Followers',
          data: result.rows.map(row => row.followers_count),
          borderColor: '#1877F2',
          tension: 0.1,
        },
        {
          label: 'Daily Change',
          data: result.rows.map(row => row.daily_change),
          borderColor: '#42B72A',
          tension: 0.1,
        }
      ]
    };

    return NextResponse.json({ followersData });
  } catch (error) {
    console.error('Error fetching Facebook history:', error);
    return NextResponse.json(
      { error: 'Failed to fetch Facebook history' },
      { status: 500 }
    );
  }
}