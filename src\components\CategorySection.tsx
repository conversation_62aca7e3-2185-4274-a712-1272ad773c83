"use client";

import { useState } from 'react';
import AddLinkForm from './AddLinkForm';

interface Link {
  id: number;
  title: string;
  url: string;
  category_id: number;
}

interface Category {
  id: number;
  name: string;
}

interface CategorySectionProps {
  categoryName: string;
  links: Link[];
  onDelete: (id: number) => void;
  onEdit: () => void;
  categories: Category[];
  onEditCategory: (category: Category) => void;
}

export default function CategorySection({ 
  categoryName, 
  links, 
  onDelete, 
  onEdit,
  categories,
  onEditCategory
}: CategorySectionProps) {
  const [editingLink, setEditingLink] = useState<Link | null>(null);

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl text-[#365F9F]">{categoryName}</h2>
        <button
          onClick={() => {
            const category = categories.find(c => c.name === categoryName);
            if (category) {
              onEditCategory(category);
            }
          }}
          className="text-gray-500 hover:text-[#365F9F] text-sm"
        >
          Edit Category
        </button>
      </div>
      
      <div className="space-y-3">
        {links.map((link) => (
          <div key={link.id}>
            {editingLink?.id === link.id ? (
              <AddLinkForm
                categories={categories}
                initialData={link}
                onAdd={() => {
                  onEdit();
                  setEditingLink(null);
                }}
                onCancel={() => setEditingLink(null)}
              />
            ) : (
              <div className="flex items-center justify-between py-2 group">
                <a
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-700 hover:text-[#365F9F] hover:underline"
                >
                  {link.title}
                </a>
                <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <button
                    onClick={() => setEditingLink(link)}
                    className="text-gray-500 hover:text-[#365F9F] mx-2"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => onDelete(link.id)}
                    className="text-gray-500 hover:text-red-600"
                  >
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
