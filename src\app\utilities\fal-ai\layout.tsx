import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';

export default function FalAiLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/utilities" className="flex items-center">
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back to Utilities
          </Link>
        </Button>
      </div>
      
      <div className="max-w-3xl mx-auto">
        {children}
      </div>
    </div>
  );
}
