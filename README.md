# Marketing & Outreach Division Dashboard

A Next.js dashboard application for the City of Fairfield's Marketing and Outreach Division.

## Tech Stack

- **Framework:** Next.js 15.2
- **Language:** TypeScript
- **Styling:** Tailwind CSS
- **Database:** PostgreSQL
- **Linting:** ESLint
- **Package Manager:** npm/yarn

## Prerequisites

- Node.js (version 18.18.0 or higher)
- npm or yarn
- PostgreSQL (version 14 or higher)

## Getting Started

1. Clone the repository:
```bash
git clone [repository-url]
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Create a `.env.local` file in the root directory and add the following environment variables:
```
# Database Configuration
POSTGRES_HOST=your_host
POSTGRES_PORT=5432
POSTGRES_DB=your_database
POSTGRES_USER=your_user
POSTGRES_PASSWORD=your_password

# Cloudinary Configuration (required for Photo Upscaler)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name

# Other configurations as needed
```

4. Run the development server:
```bash
npm run dev
# or
yarn dev
```

5. Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Features

### Dashboard Overview
- Real-time social media metrics aggregation
- Campaign performance tracking
- Popular pages analytics
- Cablecast Channel 26 integration

### Utilities
- Photo Upscaler: AI-powered image resolution enhancement using Cloudinary
  - Supports single and batch image processing
  - 2x resolution increase with superior quality
  - Supports JPG, PNG, and WebP formats

### Social Media Integration
- Facebook Page insights and metrics
- Instagram Business Account analytics
- YouTube Channel statistics
- Nextdoor community engagement tracking

### Analytics
- Google Analytics 4 integration
- Website traffic analysis
- Content performance metrics
- User engagement statistics

### Campaign Management
- Campaign creation and tracking
- Cross-platform post association
- Performance metrics visualization
- Historical data analysis

## Database Architecture

### Schema Organization

The database is organized into platform-specific schemas for better data segregation:

#### Social Media Schemas

##### Facebook Schema (`fb`)
- `pages`: Page information and metrics
- `page_history`: Historical page analytics
- `posts`: Post content and metadata
- `post_insights`: Post-specific engagement metrics
- `page_insights`: Overall page performance metrics
- `videos`: Video content analytics
- `comments`: User engagement data

##### Instagram Schema (`ig`)
- `accounts`: Business account information
- `media`: Posts and stories data
- `media_insights`: Media engagement metrics
- `account_insights`: Account-level analytics
- `comments`: User interactions
- `hashtags`: Content categorization

##### YouTube Schema (`yt`)
- `channel`: Channel statistics
- `video`: Video content metrics
- `channel_insight`: Channel analytics
- `video_insight`: Video performance data

#### Content Management Schemas

##### Cablecast Schema (`cc_26`)
- `shows`: Program information
- `schedule`: Broadcasting schedule
- Primary tracking:
  - Show counts
  - Total runtime hours
  - Viewership metrics

##### Campaign Schema (`campaign`)
- `campaigns`: Marketing campaign details
  - Title, dates, status
  - Web URL paths
  - Performance indicators
- `posts`: Associated social media content
  - Cross-platform post IDs
  - Engagement metrics
  - Content metadata

##### Digital Asset Management Schema (`dams`)
- Asset tracking
- Download statistics
- Usage analytics

## Available Scripts

- `npm run dev` - Runs the development server with Turbopack
- `npm run build` - Creates a production build
- `npm run start` - Starts the production server
- `npm run lint` - Runs ESLint to check code quality

## Project Structure

```
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   └── social-metrics/
│   │   ├── analytics/
│   │   ├── dams/
│   │   ├── newsletter/
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── components/
│   │   ├── DashboardLayout.tsx
│   │   └── SocialMetrics.tsx
│   └── lib/
│       └── db.ts
├── public/
├── .next/
└── ...configuration files
```

## Development Guidelines

### Database Queries
- Use the `sql` template literal from `@/lib/db`
- Implement proper error handling
- Use parameterized queries for security

### Component Development
- Follow TypeScript strict mode guidelines
- Implement proper loading states
- Handle error scenarios gracefully

## Deployment

The application is optimized for deployment on the [Vercel Platform](https://vercel.com/new).

For detailed deployment instructions, refer to the [Next.js deployment documentation](https://nextjs.org/docs/deployment).

## Learn More

To learn more about the technologies used in this project:

- [Next.js Documentation](https://nextjs.org/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

## Database Schema

The data is organized into platform-specific schemas within your PostgreSQL database:

### Facebook Schema (`fb`)

- `pages`: Basic page information
  - Primary key: `id` (page_id)
  - Tracks: name, category, followers_count, fan_count
  - Last updated timestamp

- `page_history`: Historical page metrics
  - Primary key: `id` (auto-incrementing)
  - Tracks historical: followers_count, fan_count
  - Captured timestamp for trend analysis

- `posts`: All posts from your page
  - Primary key: `id` (post_id)
  - Tracks: message, created_time, type, permalink_url

- `post_insights`: Engagement metrics for each post
  - Primary key: `id`
  - Foreign key: `post_id`
  - Tracks: insight_type, period, value, end_time

- `page_insights`: Overall page metrics
  - Primary key: `id`
  - Foreign key: `page_id`
  - Tracks: insight_type, period, value, end_time

- `videos`: Video content metrics
  - Primary key: `id` (video_id)
  - Tracks: title, description, created_time, length, permalink_url

- `comments`: Comments on posts
  - Primary key: `id` (comment_id)
  - Foreign key: `post_id`
  - Tracks: message, created_time, like_count

### Instagram Schema (`ig`)

- `accounts`: Instagram business account information
  - Primary key: `id`
  - Tracks: username, name, biography, followers_count, follows_count, media_count

- `media`: Instagram posts and stories
  - Primary key: `id`
  - Tracks: caption, media_type, media_url, permalink, timestamp

- `media_insights`: Engagement metrics for media
  - Primary key: `id`
  - Foreign key: `media_id`
  - Tracks: insight_type, value

- `account_insights`: Account-level metrics
  - Primary key: `id`
  - Foreign key: `account_id`
  - Tracks: insight_type, period, value, end_time

- `comments`: Comments on media
  - Primary key: `id`
  - Foreign key: `media_id`
  - Tracks: text, timestamp, username

- `hashtags`: Hashtags used in media
  - Primary key: `id`
  - Foreign key: `media_id`
  - Tracks: hashtag

### YouTube Schema (`yt`)

- `channel`: Channel information
  - Primary key: `id`
  - Tracks: title, description, subscriber_count, view_count, video_count

- `video`: Video content
  - Primary key: `id`
  - Foreign key: `channel_id`
  - Tracks: title, description, published_at, view_count, like_count, comment_count

- `channel_insight`: Channel analytics
  - Primary key: `id`
  - Foreign key: `channel_id`
  - Tracks: metric, value, date

- `video_insight`: Video-specific analytics
  - Primary key: `id`
  - Foreign key: `video_id`
  - Tracks: metric, value, date

### Cablecast Schema (`cc_26`)

- `shows`: Show information
  - Primary key: `id`
  - Tracks: title, category, comments, custom fields, captions, translations

- `schedule`: Schedule items
  - Primary key: `id`
  - Foreign keys: `channel`, `show`
  - Tracks: run_date_time, run_type, captions, translations



