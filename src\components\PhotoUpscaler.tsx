"use client";

import { useState, useRef, ChangeEvent } from 'react';

interface UploadedFile {
  id: string;
  file: File;
  preview: string;
  status: 'pending' | 'processing' | 'completed' | 'error' | 'downloading';
  result?: string;
  processedId?: string;
  format?: string;
  originalFilename?: string;
}

// No cleanup needed - Cloudinary manages storage

export default function PhotoUpscaler() {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  // Handle drag events for the drop zone
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files);
    }
  };

  // Process the files from either drag-drop or file input
  const handleFiles = (fileList: FileList) => {
    if (!fileList || fileList.length === 0) return;

    const newFiles: UploadedFile[] = Array.from(fileList).map(file => ({
      id: Math.random().toString(36).substring(2, 9),
      file,
      preview: URL.createObjectURL(file),
      status: 'pending' as const
    }));

    setFiles(prev => [...prev, ...newFiles]);
    setIsComplete(false);
  };

  // Handle file input change
  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;

    handleFiles(e.target.files);

    // Reset the input value so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // This function is replaced by the one above

  const removeFile = (id: string) => {
    setFiles(prev => {
      const updatedFiles = prev.filter(file => file.id !== id);
      // If all files are removed, reset the complete state
      if (updatedFiles.length === 0) {
        setIsComplete(false);
      }
      return updatedFiles;
    });
  };

  const handleSubmit = async () => {
    if (files.length === 0 || isProcessing) return;

    setIsProcessing(true);
    setErrorMessage(null); // Clear any previous errors

    try {
      // Update all files to processing status
      setFiles(prev => prev.map(file => ({ ...file, status: 'processing' as const })));

      // Process each file
      const processedFiles = await Promise.all(
        files.map(async (file): Promise<UploadedFile> => {
          try {
            const formData = new FormData();
            formData.append('image', file.file);

            const response = await fetch('/api/upscale', {
              method: 'POST',
              body: formData,
            });

            const result = await response.json();

            if (!response.ok) {
              // Handle specific error messages from the API
              const errorMsg = result.message || `Failed to process image: ${response.statusText}`;

              // If this is a resolution error, show it in the UI
              if (result.error === 'Image resolution too high') {
                setErrorMessage(result.message);
              }

              throw new Error(errorMsg);
            }

            return {
              ...file,
              status: 'completed' as const,
              result: result.url,
              processedId: result.id,
              format: result.format,
              originalFilename: result.originalFilename
            };
          } catch (error) {
            console.error(`Error processing file ${file.file.name}:`, error);

            // Set a generic error message if none is already set
            if (!errorMessage) {
              setErrorMessage(`Error processing ${file.file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }

            return {
              ...file,
              status: 'error' as const
            };
          }
        })
      );

      setFiles(processedFiles);
      setIsComplete(true);
    } catch (error) {
      console.error('Error during processing:', error);

      // Set a generic error message if none is already set
      if (!errorMessage) {
        setErrorMessage(`Error during processing: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDownload = async () => {
    if (files.length === 0 || !isComplete || isDownloading) return;

    setIsDownloading(true);
    setDownloadProgress(0);

    // Function to download a single file with proper handling
    const downloadFile = async (file: UploadedFile, index: number = 0) => {
      try {
        // Update status to downloading
        setFiles(prev => prev.map(f =>
          f.id === file.id ? { ...f, status: 'downloading' as const } : f
        ));

        console.log(`Attempting to download from URL: ${file.result}`);

        try {
          // Always use the proxy approach for better control and progress feedback
          const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(file.result!)}`;
          console.log(`Using proxy URL: ${proxyUrl}`);

          // Fetch the image through our proxy
          const response = await fetch(proxyUrl, {
            method: 'GET',
            cache: 'no-cache',
          });

          if (!response.ok) {
            console.error(`Proxy download failed with status: ${response.status} ${response.statusText}`);
            throw new Error(`Failed to download image: ${response.statusText}`);
          }

          // Get the blob from the response
          const blob = await response.blob();

          // Create a download link
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `upscaled_${index > 0 ? `${index}_` : ''}${file.originalFilename || file.file.name}`;

          // Trigger download
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Clean up the object URL
          URL.revokeObjectURL(url);

          return true;
        } catch (error) {
          console.error(`Download failed:`, error);
          throw error; // Re-throw to be caught by the outer try-catch
        }
      } catch (error) {
        console.error(`Error downloading file ${file.file.name}:`, error);

        // Update status to error
        setFiles(prev => prev.map(f =>
          f.id === file.id ? { ...f, status: 'error' as const } : f
        ));

        return false;
      }
    };

    try {
      if (files.length === 1 && files[0].result) {
        // Download single file
        setDownloadProgress(10); // Initial progress
        await downloadFile(files[0]);
        setDownloadProgress(100); // Complete
      } else {
        // For multiple files, download them one by one
        const completedFiles = files.filter(file => file.status === 'completed' && file.result);

        if (completedFiles.length === 0) {
          console.error('No processed files found');
          return;
        }

        // Calculate progress increment per file
        const progressIncrement = 90 / completedFiles.length;

        // Download each file and update progress
        for (let i = 0; i < completedFiles.length; i++) {
          // Update progress before each file
          setDownloadProgress(10 + i * progressIncrement);

          // Download the file
          await downloadFile(completedFiles[i], i + 1);

          // Update progress after each file
          setDownloadProgress(10 + (i + 1) * progressIncrement);

          // Add a small delay between downloads
          if (i < completedFiles.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 300));
          }
        }

        // Set final progress
        setDownloadProgress(100);
      }
    } catch (error) {
      console.error('Download failed:', error);
      // Show error in UI if needed
    } finally {
      // Short delay before resetting to ensure progress animation completes
      setTimeout(() => {
        setIsDownloading(false);
        setDownloadProgress(0);
      }, 500);
    }
  };

  return (
    <div className="space-y-6">
      {/* Error message display */}
      {errorMessage && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{errorMessage}</span>
          <button
            className="absolute top-0 bottom-0 right-0 px-4 py-3"
            onClick={() => setErrorMessage(null)}
          >
            <svg className="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <title>Close</title>
              <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
            </svg>
          </button>
        </div>
      )}

      <div
        ref={dropZoneRef}
        className={`border-2 border-dashed ${isDragging ? 'border-[#365F9F] bg-blue-50' : 'border-gray-300'} rounded-lg p-6 text-center transition-colors cursor-pointer`}
        onClick={() => fileInputRef.current?.click()}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          className="hidden"
          accept="image/*"
          multiple
        />
        <div className="space-y-3">
          <svg xmlns="http://www.w3.org/2000/svg" className={`h-12 w-12 mx-auto ${isDragging ? 'text-[#365F9F]' : 'text-gray-400'} transition-colors`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-700">Drag photos here or click to upload</h3>
          <p className="text-sm text-gray-500">Upload one or multiple photos to upscale</p>
          <button
            onClick={(e) => {
              e.stopPropagation(); // Prevent the div's onClick from firing
              fileInputRef.current?.click();
            }}
            className="mt-2 px-4 py-2 bg-[#365F9F] text-white rounded-md hover:bg-[#2A4C7F] transition-colors"
          >
            Select Photos
          </button>
        </div>
      </div>

      {files.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-700">Selected Photos ({files.length})</h3>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {files.map((file) => (
              <div key={file.id} className="relative border rounded-lg overflow-hidden">
                <div className="aspect-square relative">
                  <img
                    src={file.preview}
                    alt={file.file.name}
                    className="w-full h-full object-cover"
                  />
                  {file.status === 'processing' && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
                    </div>
                  )}
                  {file.status === 'downloading' && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 flex flex-col items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white mb-2"></div>
                      <div className="text-white text-xs font-medium">Downloading...</div>
                    </div>
                  )}
                  {file.status === 'completed' && (
                    <div className="absolute top-2 right-2 bg-green-500 text-white rounded-full p-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  )}
                  {file.status === 'error' && (
                    <div className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="p-2 bg-gray-50">
                  <p className="text-xs truncate">{file.file.name}</p>
                  <p className="text-xs text-gray-500">{(file.file.size / 1024).toFixed(1)} KB</p>
                </div>
                <button
                  onClick={() => removeFile(file.id)}
                  className="absolute top-2 left-2 bg-black bg-opacity-50 text-white rounded-full p-1 hover:bg-opacity-70 transition-opacity"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            ))}
          </div>

          <div className="flex gap-4">
            <button
              onClick={handleSubmit}
              disabled={isProcessing || files.length === 0 || isComplete}
              className={`px-6 py-2 rounded-md ${
                isProcessing || files.length === 0 || isComplete
                  ? 'bg-gray-300 cursor-not-allowed'
                  : 'bg-[#365F9F] text-white hover:bg-[#2A4C7F]'
              } transition-colors`}
            >
              {isProcessing ? 'Processing...' : 'Upscale Photos'}
            </button>

            {isComplete && (
              <button
                onClick={handleDownload}
                disabled={isDownloading}
                className={`px-6 py-2 rounded-md flex items-center justify-center relative overflow-hidden ${isDownloading ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#276A30] text-white hover:bg-[#1E5023]'} transition-colors`}
              >
                {/* Progress bar */}
                {isDownloading && (
                  <div
                    className="absolute left-0 top-0 bottom-0 bg-[#1E5023] transition-all duration-300 ease-in-out"
                    style={{ width: `${downloadProgress}%` }}
                  ></div>
                )}

                {/* Button content */}
                <div className="flex items-center justify-center relative z-10">
                  {isDownloading && (
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  )}
                  {isDownloading
                    ? (files.length === 1
                        ? `Downloading... ${downloadProgress}%`
                        : `Downloading Files... ${downloadProgress}%`)
                    : (files.length === 1 ? 'Download Photo' : 'Download All')}
                </div>
              </button>
            )}
          </div>
        </div>
      )}

      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-medium text-gray-700 mb-2">About Photo Upscaler</h3>
        <p className="text-sm text-gray-600">
          This utility uses advanced AI to increase the resolution of your photos by 2x while maintaining high quality.
          Perfect for enhancing low-resolution images for printing or digital display.
        </p>
        <ul className="mt-2 text-sm text-gray-600 list-disc list-inside">
          <li>Supports JPG, PNG, and WebP formats</li>
          <li>Maximum file size: 10MB per image</li>
          <li>Maximum resolution: 3000px width or height</li>
          <li>Uses AI-powered upscaling for superior quality</li>
          <li>Exactly 2x resolution increase with enhanced details and sharpness</li>
          <li>Preserves image quality and original format</li>
          <li>Batch processing available for multiple images</li>
          <li>For multiple files, each file will download separately</li>
        </ul>
      </div>
    </div>
  );
}


