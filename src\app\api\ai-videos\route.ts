import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET() {
  try {
    const result = await pool.query(`
      SELECT 
        id, 
        title, 
        source_image, 
        output_video, 
        model, 
        prompt,
        year,
        month,
        day,
        CASE
          WHEN month IS NULL THEN year::TEXT
          WHEN day IS NULL THEN TO_CHAR(TO_DATE(year::TEXT || '-' || month::TEXT, 'YYYY-MM'), 'YYYY-MM')
          ELSE TO_CHAR(TO_DATE(year::TEXT || '-' || month::TEXT || '-' || day::TEXT, 'YYYY-MM-DD'), 'YYYY-MM-DD')
        END as formatted_date
      FROM ai.videos
      ORDER BY id DESC
    `);

    return NextResponse.json({ videos: result.rows });
  } catch (error) {
    console.error('Error fetching AI videos:', error);
    return NextResponse.json(
      { error: 'Failed to fetch AI videos' },
      { status: 500 }
    );
  }
}