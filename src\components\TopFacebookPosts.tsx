"use client";

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import LoadingState from './LoadingState';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface DailyImpression {
  date: string;
  impressions: number;
}

interface FacebookPost {
  id: string;
  message: string;
  created_time: string;
  thumb_url: string;
  permalink_url: string;
  impressions: number;
  likes: number;
  loves: number;
  wows: number;
  angers: number;
  sads: number;
  daily_impressions: DailyImpression[];
}

const ImpressionsGraph = ({ data }: { data: DailyImpression[] }) => {
  if (!data || data.length === 0) {
    return <div className="h-40 mt-4 flex items-center justify-center text-gray-500">No impression data available</div>;
  }

  const chartData = {
    labels: data.map(d => format(new Date(d.date), 'MMM d')),
    datasets: [
      {
        label: 'Impressions',
        data: data.map(d => d.impressions),
        fill: false,
        borderColor: '#365F9F',
        tension: 0.1,
        pointRadius: 2,
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: (context: any) => `${context.parsed.y.toLocaleString()} impressions`
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(this: any, tickValue: number | string) {
            if (typeof tickValue === 'number') {
              return tickValue.toLocaleString();
            }
            return tickValue;
          }
        }
      }
    }
  };

  return (
    <div className="h-40 mt-4">
      <Line data={chartData} options={options} />
    </div>
  );
};

export default function TopFacebookPosts() {
  const [posts, setPosts] = useState<FacebookPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedPost, setExpandedPost] = useState<string | null>(null);
  const [daysBack, setDaysBack] = useState(30);

  const fetchPosts = async (days: number) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/top-facebook-posts?days=${days}`);
      if (!response.ok) throw new Error('Failed to fetch posts');
      const data = await response.json();
      setPosts(data.posts);
    } catch (err) {
      setError('Failed to load top Facebook posts');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPosts(daysBack);
  }, [daysBack]);

  if (loading) return <LoadingState />;
  if (error) return <div className="bg-white p-6 rounded-lg shadow-md text-red-600">Error: {error}</div>;

  const timeRangeButtons = [30, 14, 7, 3].map((days) => (
    <button
      key={days}
      onClick={() => setDaysBack(days)}
      className={`px-3 py-1 text-sm rounded-md transition-colors ${
        daysBack === days
          ? 'bg-[#365F9F] text-white'
          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
      }`}
    >
      {days}d
    </button>
  ));

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl text-[#365F9F]">
          Top 3 Facebook Posts - Last {daysBack} Days
        </h2>
        <div className="flex gap-2">
          {timeRangeButtons}
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {posts.map((post) => (
          <div key={post.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            {post.thumb_url && (
              <div className="aspect-video relative overflow-hidden">
                <img
                  src={post.thumb_url}
                  alt=""
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            <div className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <img
                  src="/images/city-logo.jpg"
                  alt="City of Fairfield"
                  className="w-8 h-8 rounded-full"
                />
                <div>
                  <div className="font-semibold text-gray-900">City of Fairfield</div>
                  <div className="text-xs text-gray-700">
                    {format(new Date(post.created_time), 'MMM d, yyyy')}
                  </div>
                </div>
              </div>
              <div className="text-gray-800">
                {expandedPost === post.id ? (
                  <p className="whitespace-pre-wrap">{post.message}</p>
                ) : (
                  <p className="line-clamp-3">{post.message}</p>
                )}
                {post.message.length > 150 && (
                  <button
                    onClick={() => setExpandedPost(expandedPost === post.id ? null : post.id)}
                    className="text-[#365F9F] hover:text-[#264573] font-medium text-sm mt-1"
                  >
                    {expandedPost === post.id ? 'Show less' : 'See more'}
                  </button>
                )}
              </div>
              <div className="mt-3 pt-3 border-t border-gray-100 space-y-2">
                <div className="inline-block bg-[#365F9F] text-white px-3 py-1 rounded-full text-sm font-medium">
                  {post.impressions.toLocaleString()} impressions
                </div>
                <div className="flex flex-wrap gap-3">
                  {/* Like */}
                  {post.likes > 0 && (
                    <div className="flex items-center gap-1">
                      <svg className="w-5 h-5 text-[#1877F2]" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z" />
                      </svg>
                      <span className="text-sm text-gray-700">{post.likes.toLocaleString()}</span>
                    </div>
                  )}

                  {/* Love */}
                  {post.loves > 0 && (
                    <div className="flex items-center gap-1">
                      <svg className="w-5 h-5 text-red-500" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M11.645 20.91l-.007-.003-.022-.012a15.247 15.247 0 01-.383-.218 25.18 25.18 0 01-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0112 5.052 5.5 5.5 0 0116.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 01-4.244 3.17 15.247 15.247 0 01-.383.219l-.022.012-.007.004-.003.001a.752.752 0 01-.704 0l-.003-.001z"/>
                      </svg>
                      <span className="text-sm text-gray-700">{post.loves.toLocaleString()}</span>
                    </div>
                  )}

                  {/* Wow */}
                  {post.wows > 0 && (
                    <div className="flex items-center gap-1">
                      <svg className="w-5 h-5 text-yellow-500" viewBox="0 0 512 512" fill="currentColor">
                        <path d="M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm0 349.67c-77.07 0-139.67-62.6-139.67-139.67S178.93 118.33 256 118.33 395.67 180.93 395.67 256 333.07 397.67 256 397.67zM176 211.67a28 28 0 1128-28 28 28 0 01-28 28zm152-28a28 28 0 11-28-28 28 28 0 0128 28zm-76 115.83c-46.67 0-84.67-35-84.67-78.33h169.33c0 43.33-38 78.33-84.66 78.33z"/>
                      </svg>
                      <span className="text-sm text-gray-700">{post.wows.toLocaleString()}</span>
                    </div>
                  )}

                  {/* Angry */}
                  {post.angers > 0 && (
                    <div className="flex items-center gap-1">
                      <svg className="w-5 h-5 text-orange-500" viewBox="0 0 512 512" fill="currentColor">
                        <path d="M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm0 349.67c-77.07 0-139.67-62.6-139.67-139.67S178.93 118.33 256 118.33 395.67 180.93 395.67 256 333.07 397.67 256 397.67zM176 211.67a28 28 0 1128-28 28 28 0 01-28 28zm152-28a28 28 0 11-28-28 28 28 0 0128 28zM256 367.33c-39.33 0-73.33-23.33-84.67-56h169.33c-11.33 32.67-45.33 56-84.66 56z"/>
                      </svg>
                      <span className="text-sm text-gray-700">{post.angers.toLocaleString()}</span>
                    </div>
                  )}

                  {/* Sad */}
                  {post.sads > 0 && (
                    <div className="flex items-center gap-1">
                      <svg className="w-5 h-5 text-yellow-500" viewBox="0 0 512 512" fill="currentColor">
                        <path d="M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm0 349.67c-77.07 0-139.67-62.6-139.67-139.67S178.93 118.33 256 118.33 395.67 180.93 395.67 256 333.07 397.67 256 397.67zM176 211.67a28 28 0 1128-28 28 28 0 01-28 28zm152-28a28 28 0 11-28-28 28 28 0 0128 28zM256 367.33c46.67 0 84.67-35 84.67-78.33H171.33c0 43.33 38 78.33 84.67 78.33z"/>
                      </svg>
                      <span className="text-sm text-gray-700">{post.sads.toLocaleString()}</span>
                    </div>
                  )}
                </div>
              </div>
              <div className="mt-4 pt-4 border-t border-gray-100">
                <ImpressionsGraph data={post.daily_impressions} />
              </div>
              <a
                href={post.permalink_url}
                target="_blank"
                rel="noopener noreferrer"
                className="mt-3 text-sm text-[#365F9F] hover:text-[#264573] font-medium block"
              >
                View on Facebook →
              </a>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}









