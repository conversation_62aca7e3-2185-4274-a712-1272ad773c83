"use client";

import { useState } from 'react';
import { format } from 'date-fns';
import Modal from './Modal';
import SocialEmbed from './SocialEmbed';

interface CampaignPost {
  title: string;
  date: string;
  yt_thumb: string;
  yt_id: string;
  yt_views: number;
  yt_likes: number;
  fb_video_views: number;
  fb_post_like: number;
  fb_post_love: number;
  fb_post_impressions: number;
  fb_post_id: string;
  ig_reach: number;
  ig_likes: number;
  ig_total_interactions: number;
  ig_id: string;
  ig_permalink: string;
  fb_permalink: string;
}

interface CampaignPostProps {
  post: CampaignPost;
  index: number;
}

export default function CampaignPost({ post, index }: CampaignPostProps) {
  const [activeModal, setActiveModal] = useState<'youtube' | 'facebook' | 'instagram' | null>(null);

  return (
    <>
      <div key={index} className={`${index !== 0 ? 'border-t border-gray-100 pt-6' : ''}`}>
        <div className="mb-2">
          <h3 className="text-lg text-gray-800">{post.title}</h3>
          <p className="text-sm text-gray-500">
            {format(new Date(post.date), 'EEEE, MMMM do, yyyy')}
          </p>
        </div>
        
        <div className="flex gap-4">
          {/* Thumbnail */}
          <div className="flex-1">
            <div 
              className="bg-gray-50 p-3 rounded-lg h-full overflow-hidden cursor-pointer transition-transform hover:scale-[1.02]"
              onClick={() => setActiveModal('youtube')}
            >
              <div className="w-full h-full rounded-lg overflow-hidden relative group">
                <img 
                  src={post.yt_thumb} 
                  alt={post.title}
                  className="w-full h-full object-cover rounded-lg scale-135"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-opacity flex items-center justify-center">
                  <svg className="w-12 h-12 text-white opacity-0 group-hover:opacity-100 transition-opacity" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Metrics Grid */}
          <div className="flex gap-4 flex-[3]">
            {/* YouTube Metrics */}
            <div 
              onClick={() => setActiveModal('youtube')}
              className="bg-red-50 p-3 rounded-lg flex-1 cursor-pointer transition-transform hover:scale-[1.02]"
            >
              <div className="flex items-center gap-2 text-[#FF0000] mb-2">
                <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                </svg>
                <span className="font-medium">YouTube</span>
              </div>
              <div className="text-gray-700">
                <p>{post.yt_views.toLocaleString()} views</p>
                <p>{post.yt_likes.toLocaleString()} likes</p>
              </div>
            </div>

            {/* Facebook Metrics */}
            <div 
              onClick={() => setActiveModal('facebook')}
              className="bg-blue-50 p-3 rounded-lg flex-1 cursor-pointer transition-transform hover:scale-[1.02]"
            >
              {/* ... existing Facebook metrics content ... */}
            </div>

            {/* Instagram Metrics */}
            <div 
              onClick={() => setActiveModal('instagram')}
              className="bg-purple-50 p-3 rounded-lg flex-1 cursor-pointer transition-transform hover:scale-[1.02]"
            >
              {/* ... existing Instagram metrics content ... */}
            </div>
          </div>
        </div>
      </div>

      {/* Modal */}
      <Modal
        isOpen={activeModal !== null}
        onClose={() => setActiveModal(null)}
      >
        {activeModal && (
          <SocialEmbed
            type={activeModal}
            id={
              activeModal === 'youtube' ? post.yt_id :
              activeModal === 'facebook' ? post.fb_post_id :
              post.ig_id
            }
            ig_permalink={post.ig_permalink}
            fb_permalink={post.fb_permalink}
          />
        )}
      </Modal>
    </>
  );
}


