'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChevronDown, ChevronUp, Star, StarOff, Volume2, Pause, Download, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

interface TTSGeneration {
  id: number;
  prompt: string;
  voice_actor: string;
  audio_url: string;
  created_at: string;
  favorite: boolean;
}

function ExpandableText({ text, maxLength = 150 }: { text: string; maxLength?: number }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [needsTruncation, setNeedsTruncation] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (textRef.current) {
      // Check if the text is being truncated by comparing scroll height with client height
      const element = textRef.current;
      const isTruncated = element.scrollHeight > element.clientHeight;
      setNeedsTruncation(isTruncated);
    }
  }, [text]);

  // If text is short, just render it without any truncation
  if (text.length <= maxLength && !needsTruncation) {
    return <div className="whitespace-pre-wrap">{text}</div>;
  }

  return (
    <div className="space-y-1">
      <div 
        ref={textRef}
        className={`whitespace-pre-wrap ${!isExpanded ? 'line-clamp-3' : ''}`}
      >
        {text}
      </div>
      {needsTruncation && (
        <Button
          variant="ghost"
          size="sm"
          className="h-auto p-0 text-sm text-blue-600 hover:text-blue-800 hover:bg-transparent"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? (
            <>
              Show less <ChevronUp className="ml-1 h-4 w-4" />
            </>
          ) : (
            <>
              Show more <ChevronDown className="ml-1 h-4 w-4" />
            </>
          )}
        </Button>
      )}
    </div>
  );
}

export default function TextToSpeechHistoryPage() {
  const [generations, setGenerations] = useState<TTSGeneration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentlyPlaying, setCurrentlyPlaying] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    fetchGenerations();
  }, []);

  const fetchGenerations = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/fal-tts');
      if (!response.ok) throw new Error('Failed to fetch generations');
      const data = await response.json();
      setGenerations(data.generations);
    } catch (err) {
      setError('Failed to load generation history');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const toggleFavorite = async (id: number, currentFavorite: boolean) => {
    try {
      const response = await fetch(`/api/fal-tts/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ favorite: !currentFavorite }),
      });

      if (!response.ok) throw new Error('Failed to update favorite status');
      
      // Update the local state
      setGenerations(prev => 
        prev.map(gen => 
          gen.id === id ? { ...gen, favorite: !currentFavorite } : gen
        )
      );

      toast.success(!currentFavorite ? "Added to favorites" : "Removed from favorites");
    } catch (err) {
      console.error('Error updating favorite status:', err);
      toast.error("Failed to update favorite status");
    }
  };

  const toggleAudio = (url: string) => {
    if (audioRef.current) {
      if (currentlyPlaying === url) {
        // Stop audio if it's currently playing
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
        setCurrentlyPlaying(null);
      } else {
        // Play new audio
        if (audioRef.current.src !== url) {
          audioRef.current.src = url;
        }
        audioRef.current.play().catch(err => {
          console.error('Error playing audio:', err);
          toast.error("Failed to play audio. Your browser may block autoplay.");
          setCurrentlyPlaying(null);
        });
        setCurrentlyPlaying(url);
      }
    }
  };

  // Add effect to handle audio end
  useEffect(() => {
    const audioElement = audioRef.current;
    const handleEnded = () => setCurrentlyPlaying(null);
    
    if (audioElement) {
      audioElement.addEventListener('ended', handleEnded);
    }
    
    return () => {
      if (audioElement) {
        audioElement.removeEventListener('ended', handleEnded);
      }
    };
  }, []);

  const downloadAudio = async (url: string, prompt: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      
      // Create a filename from the prompt (truncated and sanitized)
      const filename = prompt
        .substring(0, 30)
        .replace(/[^a-z0-9]/gi, '_')
        .toLowerCase();
      
      link.download = `tts_${filename}_${new Date().getTime()}.mp3`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
    } catch (err) {
      console.error('Error downloading audio:', err);
      toast.error("Failed to download audio file");
    }
  };

  const deleteGeneration = async (id: number) => {
    if (!confirm('Are you sure you want to delete this generation? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/fal-tts/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete generation');
      
      // Update the local state by removing the deleted item
      setGenerations(prev => prev.filter(gen => gen.id !== id));
      
      toast.success("Generation deleted successfully");
    } catch (err) {
      console.error('Error deleting generation:', err);
      toast.error("Failed to delete generation");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl text-[#365F9F] mb-2">Text to Speech History</h1>
          <p className="text-gray-600">View and manage your saved speech generations</p>
        </div>
        <Link href="/utilities/fal-ai/text-to-speech">
          <Button>Create New</Button>
        </Link>
      </div>

      {loading ? (
        <div className="text-center py-8">Loading generations...</div>
      ) : error ? (
        <div className="text-center py-8 text-red-500">{error}</div>
      ) : generations.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No generations found. Create your first one!</p>
        </div>
      ) : (
        <div className="space-y-4">
          {generations.map((gen) => (
            <Card key={gen.id} className={gen.favorite ? "border-yellow-300" : ""}>
              <CardHeader className="pb-2">
                <CardTitle className="flex justify-between items-start">
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-lg mb-1">
                      <ExpandableText text={gen.prompt} maxLength={150} />
                    </div>
                  </div>
                  <div className="flex gap-2 ml-4">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => toggleFavorite(gen.id, gen.favorite)}
                      className="h-8 w-8 p-0 flex items-center justify-center"
                    >
                      {gen.favorite ? <Star className="h-5 w-5 text-yellow-500" /> : <StarOff className="h-5 w-5" />}
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteGeneration(gen.id);
                      }}
                      className="h-8 w-8 p-0 flex items-center justify-center text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-gray-500 mb-2">
                  Voice: {gen.voice_actor}
                </div>
                <div className="text-xs text-gray-400 mb-4">
                  Created: {new Date(gen.created_at).toLocaleString()}
                </div>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => toggleAudio(gen.audio_url)}
                    className={currentlyPlaying === gen.audio_url ? "bg-gray-100" : ""}
                  >
                    {currentlyPlaying === gen.audio_url ? (
                      <>
                        <Pause className="h-4 w-4 mr-2" />
                        Stop
                      </>
                    ) : (
                      <>
                        <Volume2 className="h-4 w-4 mr-2" />
                        Play
                      </>
                    )}
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => downloadAudio(gen.audio_url, gen.prompt)}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <audio ref={audioRef} className="hidden" controls />
    </div>
  );
}
