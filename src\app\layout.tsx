import type { Metada<PERSON> } from "next";
import "./globals.css";
import { Inter } from "next/font/google";
import { Toaster } from "sonner";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "City of Fairfield - Marketing & Outreach Division",
  description: "Marketing and Outreach Portal for the City of Fairfield",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <script async defer crossOrigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v17.0" nonce="your_nonce_here"></script>
      </head>
      <body>
        {children}
        <Toaster position="top-right" />
      </body>
    </html>
  );
}



