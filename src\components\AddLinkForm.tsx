"use client";

import { useState } from 'react';

interface Category {
  id: number;
  name: string;
}

interface AddLinkFormProps {
  categories: Category[];
  onAdd: () => void;
  onCancel: () => void;
  initialData?: {
    id?: number;
    title: string;
    url: string;
    category_id: number;
  };
}

export default function AddLinkForm({ categories, onAdd, onCancel, initialData }: AddLinkFormProps) {
  const [title, setTitle] = useState(initialData?.title || '');
  const [url, setUrl] = useState(initialData?.url || '');
  const [categoryId, setCategoryId] = useState(initialData?.category_id || categories[0]?.id);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsSubmitting(true);

    const endpoint = initialData?.id 
      ? `/api/links/${initialData.id}`
      : '/api/links';
    
    const method = initialData?.id ? 'PUT' : 'POST';

    try {
      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          url,
          category_id: categoryId,
        }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to save link');
      }

      await onAdd();
    } catch (error) {
      console.error('Error saving link:', error);
      setError(error instanceof Error ? error.message : 'Failed to save link');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="bg-gray-50 p-6 rounded-lg space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
        <input
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="mt-1 block w-full px-4 py-3 text-gray-900 bg-white border border-gray-300 rounded-lg shadow-sm focus:border-[#365F9F] focus:ring-[#365F9F] text-base"
          required
          disabled={isSubmitting}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">URL</label>
        <input
          type="url"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          className="mt-1 block w-full px-4 py-3 text-gray-900 bg-white border border-gray-300 rounded-lg shadow-sm focus:border-[#365F9F] focus:ring-[#365F9F] text-base"
          required
          disabled={isSubmitting}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
        <select
          value={categoryId}
          onChange={(e) => setCategoryId(Number(e.target.value))}
          className="mt-1 block w-full px-4 py-3 text-gray-900 bg-white border border-gray-300 rounded-lg shadow-sm focus:border-[#365F9F] focus:ring-[#365F9F] text-base"
          required
          disabled={isSubmitting}
        >
          {categories.map((category) => (
            <option key={category.id} value={category.id}>
              {category.name}
            </option>
          ))}
        </select>
      </div>

      {error && (
        <p className="mt-2 text-sm text-red-600">{error}</p>
      )}

      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-6 py-3 text-base font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
          disabled={isSubmitting}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-6 py-3 text-base font-medium text-white bg-[#365F9F] rounded-lg hover:bg-[#2b4c80] disabled:opacity-50"
          disabled={isSubmitting}
        >
          {isSubmitting 
            ? 'Saving...' 
            : initialData?.id 
              ? 'Update Link' 
              : 'Add Link'
          }
        </button>
      </div>
    </form>
  );
}

