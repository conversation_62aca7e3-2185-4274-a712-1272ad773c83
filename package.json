{"name": "mod-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --no-lint", "start": "next start", "lint": "next lint"}, "dependencies": {"@fal-ai/client": "^1.4.0", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-slot": "^1.2.2", "@types/pg": "^8.11.11", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.510.0", "next": "15.2.2", "pg": "^8.14.1", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-intersection-observer": "^9.16.0", "sonner": "^1.0.0", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.2.2", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}