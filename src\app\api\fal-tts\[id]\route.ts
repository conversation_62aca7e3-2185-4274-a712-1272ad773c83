import { NextResponse } from 'next/server';
import pool from '@/lib/db';

// PATCH - Update a TTS generation (e.g., toggle favorite)
export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID format' },
        { status: 400 }
      );
    }

    const { favorite } = await request.json();
    
    // Only allow updating the favorite status for now
    if (favorite === undefined) {
      return NextResponse.json(
        { error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    const result = await pool.query(
      `UPDATE fal_tts.generations
       SET favorite = $1
       WHERE id = $2
       RETURNING id, prompt, voice_actor, audio_url, created_at, favorite`,
      [favorite, id]
    );

    if (result.rowCount === 0) {
      return NextResponse.json(
        { error: 'Generation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating TTS generation:', error);
    return NextResponse.json(
      { error: 'Failed to update TTS generation' },
      { status: 500 }
    );
  }
}

// DELETE - Remove a TTS generation
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID format' },
        { status: 400 }
      );
    }

    const result = await pool.query(
      `DELETE FROM fal_tts.generations
       WHERE id = $1
       RETURNING id`,
      [id]
    );

    if (result.rowCount === 0) {
      return NextResponse.json(
        { error: 'Generation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true, id });
  } catch (error) {
    console.error('Error deleting TTS generation:', error);
    return NextResponse.json(
      { error: 'Failed to delete TTS generation' },
      { status: 500 }
    );
  }
}

