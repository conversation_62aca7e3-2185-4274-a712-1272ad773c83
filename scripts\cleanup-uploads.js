/**
 * This script can be run as a scheduled task or cron job to clean up old files
 * in the uploads directory.
 * 
 * Example cron job (runs every hour):
 * 0 * * * * node /path/to/scripts/cleanup-uploads.js
 */

const https = require('https');
const http = require('http');

// Configuration
const CLEANUP_SECRET = 'your-secret-key-here'; // Must match the secret in the API route
const API_URL = 'http://localhost:3000/api/cleanup-uploads'; // Update with your actual URL in production

// Function to make HTTP request
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;
    const requestUrl = new URL(url);
    
    const options = {
      hostname: requestUrl.hostname,
      port: requestUrl.port || (url.startsWith('https') ? 443 : 80),
      path: `${requestUrl.pathname}?secret=${CLEANUP_SECRET}`,
      method: 'GET',
    };
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(data);
          resolve(parsedData);
        } catch (e) {
          reject(new Error(`Failed to parse response: ${e.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.end();
  });
}

// Main function
async function main() {
  try {
    console.log('Starting uploads cleanup...');
    const result = await makeRequest(API_URL);
    console.log('Cleanup result:', result);
  } catch (error) {
    console.error('Error during cleanup:', error);
    process.exit(1);
  }
}

// Run the script
main();
