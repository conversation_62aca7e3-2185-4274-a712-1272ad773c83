-- Create tables in the kb schema for links management

-- Categories table
CREATE TABLE IF NOT EXISTS kb.categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Links table
CREATE TABLE IF NOT EXISTS kb.links (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    url TEXT NOT NULL,
    category_id INTEGER REFERENCES kb.categories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster category-based queries
CREATE INDEX IF NOT EXISTS idx_links_category ON kb.links(category_id);

-- Create function to update timestamp
CREATE OR <PERSON><PERSON>LACE FUNCTION update_updated_at_column()
<PERSON><PERSON>URNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updating timestamps
CREATE TRIGGER update_categories_modtime
    BEFORE UPDATE ON kb.categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_links_modtime
    BEFORE UPDATE ON kb.links
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add some initial categories as examples
INSERT INTO kb.categories (name) VALUES
    ('Social Media'),
    ('City Resources'),
    ('Marketing Tools'),
    ('Analytics Platforms')
ON CONFLICT (name) DO NOTHING;