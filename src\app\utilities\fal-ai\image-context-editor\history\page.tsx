'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChevronDown, ChevronUp, Star, StarOff, Download, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

interface KontextGeneration {
  id: number;
  prompt: string;
  original_image_url: string;
  result_image_url: string;
  created_at: string;
  favorite: boolean;
}

function ExpandableText({ text, maxLength = 150 }: { text: string; maxLength?: number }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [needsTruncation, setNeedsTruncation] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (textRef.current) {
      // Check if the text is being truncated by comparing scroll height with client height
      const element = textRef.current;
      const isTruncated = element.scrollHeight > element.clientHeight;
      setNeedsTruncation(isTruncated);
    }
  }, [text]);

  // If text is short, just render it without any truncation
  if (text.length <= maxLength && !needsTruncation) {
    return <div className="whitespace-pre-wrap">{text}</div>;
  }

  return (
    <div className="space-y-1">
      <div 
        ref={textRef}
        className={`whitespace-pre-wrap ${!isExpanded ? 'line-clamp-3' : ''}`}
      >
        {text}
      </div>
      {needsTruncation && (
        <Button
          variant="ghost"
          size="sm"
          className="h-auto p-0 text-sm text-blue-600 hover:text-blue-800 hover:bg-transparent"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? (
            <>
              Show less <ChevronUp className="ml-1 h-4 w-4" />
            </>
          ) : (
            <>
              Show more <ChevronDown className="ml-1 h-4 w-4" />
            </>
          )}
        </Button>
      )}
    </div>
  );
}

export default function ImageContextEditorHistoryPage() {
  const [generations, setGenerations] = useState<KontextGeneration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchGenerations();
  }, []);

  const fetchGenerations = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/fal-kontext');
      if (!response.ok) throw new Error('Failed to fetch generations');
      const data = await response.json();
      setGenerations(data.generations);
    } catch (err) {
      setError('Failed to load generation history');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const toggleFavorite = async (id: number, currentFavorite: boolean) => {
    try {
      const response = await fetch(`/api/fal-kontext/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ favorite: !currentFavorite }),
      });

      if (!response.ok) throw new Error('Failed to update favorite status');
      
      // Update the local state
      setGenerations(prev => 
        prev.map(gen => 
          gen.id === id ? { ...gen, favorite: !currentFavorite } : gen
        )
      );

      toast.success(!currentFavorite ? "Added to favorites" : "Removed from favorites");
    } catch (err) {
      console.error('Error updating favorite status:', err);
      toast.error("Failed to update favorite status");
    }
  };

  const downloadImage = async (url: string, prompt: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      
      // Create a filename from the prompt (truncated and sanitized)
      const filename = prompt
        .substring(0, 30)
        .replace(/[^a-z0-9]/gi, '_')
        .toLowerCase();
      
      link.download = `kontext_${filename}_${new Date().getTime()}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
    } catch (err) {
      console.error('Error downloading image:', err);
      toast.error("Failed to download image file");
    }
  };

  const deleteGeneration = async (id: number) => {
    if (!confirm('Are you sure you want to delete this generation? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/fal-kontext/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete generation');
      
      // Update the local state by removing the deleted item
      setGenerations(prev => prev.filter(gen => gen.id !== id));
      
      toast.success("Generation deleted successfully");
    } catch (err) {
      console.error('Error deleting generation:', err);
      toast.error("Failed to delete generation");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl text-[#365F9F] mb-2">Image Context Editor History</h1>
          <p className="text-gray-600">View and manage your saved image edits</p>
        </div>
        <Link href="/utilities/fal-ai/image-context-editor">
          <Button>Create New</Button>
        </Link>
      </div>

      {loading ? (
        <div className="text-center py-8">Loading generations...</div>
      ) : error ? (
        <div className="text-center py-8 text-red-500">{error}</div>
      ) : generations.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No generations found. Create your first one!</p>
        </div>
      ) : (
        <div className="space-y-4">
          {generations.map((gen) => (
            <Card key={gen.id} className={gen.favorite ? "border-yellow-300" : ""}>
              <CardHeader className="pb-2">
                <CardTitle className="flex justify-between items-start">
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-lg mb-1">
                      <ExpandableText text={gen.prompt} maxLength={150} />
                    </div>
                  </div>
                  <div className="flex gap-2 ml-4">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => toggleFavorite(gen.id, gen.favorite)}
                      className="h-8 w-8 p-0 flex items-center justify-center"
                    >
                      {gen.favorite ? <Star className="h-5 w-5 text-yellow-500" /> : <StarOff className="h-5 w-5" />}
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteGeneration(gen.id);
                      }}
                      className="h-8 w-8 p-0 flex items-center justify-center text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xs text-gray-400 mb-4">
                  Created: {new Date(gen.created_at).toLocaleString()}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <p className="text-sm font-medium mb-2">Original Image:</p>
                    <div className="border border-gray-200 rounded-md overflow-hidden">
                      <img 
                        src={gen.original_image_url} 
                        alt="Original" 
                        className="w-full h-auto object-contain"
                      />
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium mb-2">Edited Image:</p>
                    <div className="border border-gray-200 rounded-md overflow-hidden">
                      <img 
                        src={gen.result_image_url} 
                        alt="Result" 
                        className="w-full h-auto object-contain"
                      />
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => downloadImage(gen.result_image_url, gen.prompt)}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
