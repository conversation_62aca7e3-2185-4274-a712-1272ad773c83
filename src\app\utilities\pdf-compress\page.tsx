"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import PDFCompressor from '@/components/PDFCompressor';

export default function PDFCompressPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl text-[#365F9F] mb-2">PDF Compress</h1>
        <p className="text-gray-600">Compress PDF files to reduce file size while maintaining quality</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Compress Your PDFs</CardTitle>
          <CardDescription>
            Upload PDF files to compress them and reduce file size. Supports various compression methods and options.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PDFCompressor />
        </CardContent>
      </Card>
    </div>
  );
}
