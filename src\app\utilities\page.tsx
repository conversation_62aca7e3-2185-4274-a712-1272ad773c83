import DashboardLayout from '@/components/DashboardLayout';
import PhotoUpscaler from '@/components/PhotoUpscaler';
import Link from 'next/link';
import { Volume2, Sparkles, Image as ImageIcon, Edit } from 'lucide-react';

export default function UtilitiesPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl text-[#365F9F] mb-2">Utilities</h1>
          <p className="text-gray-600">Handy tools for processing images, audio, and more.</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl text-[#365F9F] mb-4">AI Tools</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <Link 
              href="/utilities/photo-upscaler"
              className="border border-gray-200 rounded-lg p-6 hover:bg-gray-50 transition-colors flex flex-col items-center text-center"
            >
              <div className="bg-blue-100 p-3 rounded-full mb-3">
                <ImageIcon className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-medium">Photo Upscaler</h3>
              <p className="text-sm text-gray-500 mt-2">Enhance and upscale your images using AI</p>
            </Link>
            
            <Link 
              href="/utilities/fal-ai/text-to-speech"
              className="border border-gray-200 rounded-lg p-6 hover:bg-gray-50 transition-colors flex flex-col items-center text-center"
            >
              <div className="bg-purple-100 p-3 rounded-full mb-3">
                <Volume2 className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-medium">Text to Speech</h3>
              <p className="text-sm text-gray-500 mt-2">Convert text to natural-sounding speech</p>
            </Link>
            
            <Link 
              href="/utilities/fal-ai/image-context-editor"
              className="border border-gray-200 rounded-lg p-6 hover:bg-gray-50 transition-colors flex flex-col items-center text-center"
            >
              <div className="bg-green-100 p-3 rounded-full mb-3">
                <Edit className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-medium">Image Context Editor</h3>
              <p className="text-sm text-gray-500 mt-2">Edit images with AI-powered context understanding</p>
            </Link>
            
            {/* Placeholder for future AI tools */}
            <div className="border border-gray-200 rounded-lg p-6 bg-gray-50 flex flex-col items-center text-center opacity-60">
              <div className="bg-gray-100 p-3 rounded-full mb-3">
                <Sparkles className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="font-medium text-gray-500">Coming Soon</h3>
              <p className="text-sm text-gray-500 mt-2">More AI tools in development</p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

