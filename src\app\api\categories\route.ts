import { NextResponse } from 'next/server';
import pool from '@/lib/db';

// GET all categories
export async function GET() {
  try {
    const result = await pool.query(
      'SELECT * FROM kb.categories ORDER BY name'
    );
    return NextResponse.json({ categories: result.rows });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

// POST new category
export async function POST(request: Request) {
  try {
    const { name } = await request.json();
    
    const result = await pool.query(
      `INSERT INTO kb.categories (name)
       VALUES ($1)
       RETURNING id, name`,
      [name]
    );

    return NextResponse.json(result.rows[0]);
  } catch (error) {
    console.error('Error creating category:', error);
    return NextResponse.json(
      { error: 'Failed to create category' },
      { status: 500 }
    );
  }
}