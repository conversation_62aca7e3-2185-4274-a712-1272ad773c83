"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import PhotoUpscaler from '@/components/PhotoUpscaler';

export default function PhotoUpscalerPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl text-[#365F9F] mb-2">Photo Upscaler</h1>
        <p className="text-gray-600">Enhance your images with AI-powered upscaling</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Enhance Your Images</CardTitle>
          <CardDescription>
            Upload images to upscale them using AI. Supports JPG, PNG, and WebP formats.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PhotoUpscaler />
        </CardContent>
      </Card>
    </div>
  );
}
