'use client';

import { useState, useRef } from 'react';
import { fal } from '@fal-ai/client';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Upload, Download, Image as ImageIcon, History } from 'lucide-react';
import { toast } from "sonner";
import Link from 'next/link';

// Define the expected response type from the Kontext API
interface KontextResponse {
  images: {
    url: string;
    width: number;
    height: number;
  }[];
  prompt: string;
}

export default function ImageContextEditorPage() {
  const [prompt, setPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [resultImage, setResultImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handlePromptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPrompt(e.target.value);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Check if the file is an image
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }
      
      setImageFile(file);
      
      // Create a preview
      const reader = new FileReader();
      reader.onload = (event) => {
        setImagePreview(event.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || !imageFile) {
      toast.error('Please provide both a prompt and an image');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResultImage(null);

    try {
      // Initialize fal client with your API key from environment variables
      const apiKey = process.env.NEXT_PUBLIC_FAL_KEY;
      if (!apiKey) {
        throw new Error('Fal.ai API key is not configured');
      }
      
      console.log('Initializing fal.ai client with key:', apiKey ? '***' + apiKey.slice(-4) : 'not set');
      
      // Configure fal client with API key
      fal.config({
        credentials: apiKey
      });
      
      // Upload the image file to fal.ai storage
      console.log('Uploading image to fal.ai storage...');
      const imageUrl = await fal.storage.upload(imageFile);
      console.log('Image uploaded, URL:', imageUrl);
      
      // Set a global timeout for fetch requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 120000); // 120 seconds timeout
      
      console.log('Sending request to fal.ai Kontext Max...');
      
      // Make the API call with detailed logging
      const result = await fal.subscribe('fal-ai/flux-pro/kontext/max', {
        input: {
          prompt: prompt,
          image_url: imageUrl
        },
        logs: true,
        onQueueUpdate: (update) => {
          console.log('Queue update:', update.status);
          if (update.status === 'IN_PROGRESS' && update.logs) {
            update.logs.forEach(log => console.log('Queue log:', log.message));
          }
        }
      }).catch(err => {
        console.error('Fal.ai API error:', err);
        if (err.status) console.error('Status:', err.status);
        if (err.message) console.error('Message:', err.message);
        if (err.response) console.error('Response:', err.response);
        throw new Error(`Failed to process image: ${err.message || 'Unknown error'}`);
      }) as unknown as KontextResponse;

      console.log('Raw API response:', JSON.stringify(result, null, 2));

      // The response might be nested under data property
      const responseData = (result as any).data || result;
      console.log('Processed response data:', responseData);

      if (!responseData.images || responseData.images.length === 0) {
        console.error('No images in response:', responseData);
        throw new Error('No images received in the API response');
      }

      // Get the first image URL from the response
      const resultImageUrl = responseData.images[0].url;
      setResultImage(resultImageUrl);
      
      // Save the generation to the database (you can implement this API endpoint later)
      try {
        const saveResponse = await fetch('/api/fal-kontext', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: prompt,
            originalImageUrl: imageUrl,
            resultImageUrl: resultImageUrl,
            favorite: false
          }),
        });

        if (!saveResponse.ok) {
          console.error('Failed to save generation:', await saveResponse.text());
          toast.error("Image was generated but couldn't be saved to history.");
        } else {
          toast.success("Image generated and saved to history.");
        }
      } catch (saveError) {
        console.error('Error saving generation:', saveError);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('Error in image processing:', {
        error: err,
        message: errorMessage,
        timestamp: new Date().toISOString()
      });
      setError(`Error: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const downloadImage = async () => {
    if (!resultImage) return;
    
    try {
      // Fetch the image as a blob
      const response = await fetch(resultImage);
      const blob = await response.blob();
      
      // Create a blob URL
      const blobUrl = window.URL.createObjectURL(blob);
      
      // Create a link element
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `edited-image-${new Date().getTime()}.jpg`;
      
      // Append to body, click, and clean up
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Release the blob URL
      window.URL.revokeObjectURL(blobUrl);
    } catch (err) {
      console.error('Error downloading image:', err);
      toast.error('Failed to download image');
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl text-[#365F9F] mb-2">Image Context Editor</h1>
        <p className="text-gray-600">Edit images with AI-powered context understanding</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Edit Image with AI</CardTitle>
          <CardDescription>
            Describe how you want to edit your image and upload an image to process.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="prompt">Editing Instructions</Label>
              <Textarea
                id="prompt"
                value={prompt}
                onChange={handlePromptChange}
                placeholder="Describe how you want to edit the image. For example: 'Add a cat sitting on the couch' or 'Change the background to a beach scene'"
                className="min-h-[120px]"
                disabled={isLoading}
                required
              />
              <p className="text-sm text-muted-foreground">
                Be specific about what changes you want to make to the image.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="image">Upload Image</Label>
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isLoading}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Select Image
                </Button>
                <input
                  ref={fileInputRef}
                  id="image"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                  disabled={isLoading}
                />
                {imageFile && (
                  <span className="text-sm text-gray-500">
                    {imageFile.name} ({Math.round(imageFile.size / 1024)} KB)
                  </span>
                )}
              </div>
            </div>

            {imagePreview && (
              <div className="mt-4">
                <p className="text-sm font-medium mb-2">Original Image:</p>
                <div className="border border-gray-200 rounded-md overflow-hidden max-w-md">
                  <img 
                    src={imagePreview} 
                    alt="Preview" 
                    className="w-full h-auto object-contain"
                  />
                </div>
              </div>
            )}

            <div className="flex items-center gap-2 pt-4">
              <Button 
                type="submit" 
                disabled={isLoading || !prompt.trim() || !imageFile}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <ImageIcon className="mr-2 h-4 w-4" />
                    Generate Edited Image
                  </>
                )}
              </Button>
            </div>

            {error && (
              <div className="text-red-500 text-sm mt-2">{error}</div>
            )}
          </form>

          {resultImage && (
            <div className="mt-8 space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Result:</h3>
                <div className="border border-gray-200 rounded-md overflow-hidden max-w-md">
                  <img 
                    src={resultImage} 
                    alt="Edited" 
                    className="w-full h-auto object-contain"
                  />
                </div>
              </div>
              <Button
                type="button"
                onClick={downloadImage}
              >
                <Download className="mr-2 h-4 w-4" />
                Download Edited Image
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex justify-end mt-8">
        <Link href="/utilities/fal-ai/image-context-editor/history">
          <Button className="gap-3 text-base font-medium px-8 py-6 h-14" size="lg" variant="default">
            <History className="h-5 w-5" />
            View History
          </Button>
        </Link>
      </div>
    </div>
  );
}