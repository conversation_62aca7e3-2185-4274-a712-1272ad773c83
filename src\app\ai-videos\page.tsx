'use client';

import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import VideoPlayer from '@/components/VideoPlayer';

interface AIVideo {
  id: number;
  title: string;
  source_image: string;
  output_video: string;
  model: string;
  prompt: string;
  year: number;
  month: number | null;
  day: number | null;
  formatted_date: string;
}

export default function AIVideosPage() {
  const [videos, setVideos] = useState<AIVideo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVideos = async () => {
      try {
        const response = await fetch('/api/ai-videos');
        if (!response.ok) throw new Error('Failed to fetch videos');
        const data = await response.json();
        setVideos(data.videos);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchVideos();
  }, []);

  const formatDate = (video: AIVideo) => {
    if (!video.month) return video.year.toString();
    if (!video.day) {
      return new Date(video.year, video.month - 1).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long'
      });
    }
    return new Date(video.year, video.month - 1, video.day).toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#365F9F]"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="text-center text-red-600 mt-8">
          <p>Error: {error}</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl text-[#365F9F] mb-2">AI Videos</h1>
          <p className="text-gray-600">AI-generated videos from source images</p>
        </div>

        <div className="space-y-4">
          {videos.map((video) => (
            <div key={video.id} className="bg-white rounded-lg shadow-md p-6">
              <div className="flex flex-wrap gap-8">
                {/* Thumbnail Column */}
                <div className="flex-1 min-w-[280px] max-w-[350px]">
                  <h2 className="text-lg font-semibold text-[#365F9F] truncate mb-3">{video.title}</h2>
                  <img
                    src={video.source_image}
                    alt={`Source image for ${video.title}`}
                    className="w-full rounded-md"
                  />
                </div>

                {/* Middle Section */}
                <div className="flex-1 min-w-[280px] pt-[43px]">
                  {/* Prompt */}
                  <div>
                    <h3 className="font-medium text-gray-700 mb-2">Prompt:</h3>
                    <p className="text-gray-600">{video.prompt}</p>
                  </div>

                  {/* Model & Date */}
                  <div className="flex gap-8 mt-6">
                    <div>
                      <h3 className="font-medium text-gray-700 mb-2">Model:</h3>
                      <p className="text-gray-600">{video.model}</p>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-700 mb-2">Date:</h3>
                      <p className="text-gray-600">{formatDate(video)}</p>
                    </div>
                  </div>
                </div>

                {/* Video Output Column */}
                <div className="flex-1 min-w-[280px] max-w-[350px] pt-[43px]">
                  <VideoPlayer url={video.output_video} />
                </div>
              </div>
            </div>
          ))}
        </div>

        {videos.length === 0 && (
          <div className="text-center text-gray-500 mt-8">
            <p>No AI videos found.</p>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}














