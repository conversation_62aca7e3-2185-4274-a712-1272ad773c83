import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const days = parseInt(searchParams.get('days') || '30');

  try {
    const result = await pool.query(`
      WITH RecentPosts AS (
        SELECT id, message, created_time, thumb_url, permalink_url
        FROM fb.posts
        WHERE created_time >= CURRENT_DATE - INTERVAL '${days} days'
          AND created_time <= CURRENT_DATE
      ),
      DailyInsights AS (
        SELECT DISTINCT ON (post_id, DATE(last_updated))
          post_id,
          DATE(last_updated) as date,
          value as impressions,
          last_updated
        FROM fb.post_insights
        WHERE insight_type = 'post_impressions'
          AND period = 'lifetime'
          AND last_updated >= CURRENT_DATE - INTERVAL '${days} days'
        ORDER BY post_id, DATE(last_updated), last_updated DESC
      ),
      LatestInsights AS (
        SELECT DISTINCT ON (post_id) 
          post_id,
          value as impressions
        FROM fb.post_insights
        WHERE insight_type = 'post_impressions'
          AND period = 'lifetime'
        ORDER BY post_id, last_updated DESC
      ),
      LatestReactions AS (
        SELECT DISTINCT ON (post_id, insight_type) 
          post_id,
          insight_type,
          value
        FROM fb.post_insights
        WHERE insight_type IN (
          'post_reactions_by_type_total_like',
          'post_reactions_by_type_total_love',
          'post_reactions_by_type_total_wow',
          'post_reactions_by_type_total_anger',
          'post_reactions_by_type_total_sad'
        )
        ORDER BY post_id, insight_type, last_updated DESC
      )
      SELECT 
        p.id,
        p.message,
        p.created_time,
        p.thumb_url,
        p.permalink_url,
        COALESCE(i.impressions, 0) as impressions,
        MAX(CASE WHEN r.insight_type = 'post_reactions_by_type_total_like' THEN r.value ELSE 0 END) as likes,
        MAX(CASE WHEN r.insight_type = 'post_reactions_by_type_total_love' THEN r.value ELSE 0 END) as loves,
        MAX(CASE WHEN r.insight_type = 'post_reactions_by_type_total_wow' THEN r.value ELSE 0 END) as wows,
        MAX(CASE WHEN r.insight_type = 'post_reactions_by_type_total_anger' THEN r.value ELSE 0 END) as angers,
        MAX(CASE WHEN r.insight_type = 'post_reactions_by_type_total_sad' THEN r.value ELSE 0 END) as sads,
        (
          SELECT json_agg(json_build_object(
            'date', di.date,
            'impressions', di.impressions
          ) ORDER BY di.date)
          FROM DailyInsights di
          WHERE di.post_id = p.id
        ) as daily_impressions
      FROM RecentPosts p
      LEFT JOIN LatestInsights i ON i.post_id = p.id
      LEFT JOIN LatestReactions r ON r.post_id = p.id
      GROUP BY p.id, p.message, p.created_time, p.thumb_url, p.permalink_url, i.impressions
      ORDER BY i.impressions DESC NULLS LAST
      LIMIT 3
    `);

    return NextResponse.json({ posts: result.rows });
  } catch (error) {
    console.error('Error fetching top Facebook posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch top Facebook posts' },
      { status: 500 }
    );
  }
}




