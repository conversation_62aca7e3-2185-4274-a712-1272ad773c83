import DashboardLayout from '@/components/DashboardLayout';

export default function NewsletterPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl text-[#365F9F] mb-2">Newsletter</h1>
          <p className="text-gray-600">Create, manage, and track email newsletters for the community.</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl text-[#365F9F] mb-4">Newsletter Campaigns</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <thead>
                <tr className="bg-gray-50 border-b">
                  <th className="text-left py-3 px-4 font-semibold text-gray-600">Name</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600">Status</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600">Sent Date</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600">Open Rate</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b hover:bg-gray-50">
                  <td className="py-3 px-4">March Community Update</td>
                  <td className="py-3 px-4">
                    <span className="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full">
                      Sent
                    </span>
                  </td>
                  <td className="py-3 px-4">Mar 5, 2025</td>
                  <td className="py-3 px-4">42.5%</td>
                  <td className="py-3 px-4">
                    <button className="text-blue-600 hover:text-blue-800 mr-2">View</button>
                    <button className="text-blue-600 hover:text-blue-800">Duplicate</button>
                  </td>
                </tr>
                <tr className="border-b hover:bg-gray-50">
                  <td className="py-3 px-4">Spring Festival Announcement</td>
                  <td className="py-3 px-4">
                    <span className="bg-yellow-100 text-yellow-800 text-xs px-3 py-1 rounded-full">
                      Draft
                    </span>
                  </td>
                  <td className="py-3 px-4">—</td>
                  <td className="py-3 px-4">—</td>
                  <td className="py-3 px-4">
                    <button className="text-blue-600 hover:text-blue-800 mr-2">Edit</button>
                    <button className="text-blue-600 hover:text-blue-800">Send</button>
                  </td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="py-3 px-4">Community Center Programs</td>
                  <td className="py-3 px-4">
                    <span className="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">
                      Scheduled
                    </span>
                  </td>
                  <td className="py-3 px-4">Apr 1, 2025</td>
                  <td className="py-3 px-4">—</td>
                  <td className="py-3 px-4">
                    <button className="text-blue-600 hover:text-blue-800 mr-2">Edit</button>
                    <button className="text-blue-600 hover:text-blue-800">Cancel</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-bold text-[#365F9F] mb-4">Subscriber Growth</h2>
            <div className="flex items-center justify-center h-40 bg-gray-50 rounded-md">
              <p className="text-gray-500">Subscriber growth chart will appear here</p>
            </div>
            <div className="mt-4">
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Total Subscribers</span>
                <span className="font-bold">2,543</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Growth Rate</span>
                <span className="font-bold text-green-600">+5.2%</span>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-bold text-[#365F9F] mb-4">Open Rate</h2>
            <div className="flex items-center justify-center h-40 bg-gray-50 rounded-md">
              <p className="text-gray-500">Open rate chart will appear here</p>
            </div>
            <div className="mt-4">
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Average Open Rate</span>
                <span className="font-bold">38.7%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Industry Average</span>
                <span className="font-bold text-gray-600">24.8%</span>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-bold text-[#365F9F] mb-4">Click Rate</h2>
            <div className="flex items-center justify-center h-40 bg-gray-50 rounded-md">
              <p className="text-gray-500">Click rate chart will appear here</p>
            </div>
            <div className="mt-4">
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Average Click Rate</span>
                <span className="font-bold">12.3%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Industry Average</span>
                <span className="font-bold text-gray-600">7.5%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
