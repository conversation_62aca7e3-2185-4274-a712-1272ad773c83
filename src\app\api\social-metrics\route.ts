import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET() {
  try {
    const fbQuery = 'SELECT followers_count FROM fb.pages LIMIT 1';
    const fbHistoryQuery = `
      SELECT followers_count
      FROM fb.page_history
      WHERE captured_at = (
        SELECT MIN(captured_at)
        FROM fb.page_history
        WHERE captured_at >= NOW() - INTERVAL '30 days'
      )
      OR captured_at = (
        SELECT MIN(captured_at)
        FROM fb.page_history
      )
      ORDER BY captured_at ASC
      LIMIT 1;
    `;
    const igQuery = 'SELECT followers_count FROM ig.accounts LIMIT 1';
    const igHistoryQuery = `
      SELECT followers_count
      FROM ig.accounts_history
      WHERE captured_at = (
        SELECT MIN(captured_at)
        FROM ig.accounts_history
        WHERE captured_at >= NOW() - INTERVAL '30 days'
      )
      OR captured_at = (
        SELECT MIN(captured_at)
        FROM ig.accounts_history
      )
      ORDER BY captured_at ASC
      LIMIT 1;
    `;
    const ytQuery = 'SELECT subscriber_count FROM yt.channel LIMIT 1';
    const ytHistoryQuery = `
      SELECT subscriber_count
      FROM yt.channel_history
      WHERE captured_at = (
        SELECT MIN(captured_at)
        FROM yt.channel_history
        WHERE captured_at >= NOW() - INTERVAL '30 days'
      )
      OR captured_at = (
        SELECT MIN(captured_at)
        FROM yt.channel_history
      )
      ORDER BY captured_at ASC
      LIMIT 1;
    `;
    const ndQuery = `
      SELECT verified_neighbors_count 
      FROM nd.nextdoor_monthly_analytics 
      WHERE report_generated_date = (
        SELECT MAX(report_generated_date) 
        FROM nd.nextdoor_monthly_analytics
      )
      LIMIT 1
    `;
    const ndHistoryQuery = `
      WITH current_month AS (
        SELECT verified_neighbors_count, report_period
        FROM nd.nextdoor_monthly_analytics
        WHERE report_period = (
          SELECT MAX(report_period)
          FROM nd.nextdoor_monthly_analytics
        )
      ),
      previous_month AS (
        SELECT verified_neighbors_count, report_period
        FROM nd.nextdoor_monthly_analytics
        WHERE report_period = (
          SELECT MAX(report_period)
          FROM nd.nextdoor_monthly_analytics
          WHERE report_period < (SELECT report_period FROM current_month)
        )
      )
      SELECT 
        c.verified_neighbors_count as current_count,
        p.verified_neighbors_count as previous_count
      FROM current_month c
      LEFT JOIN previous_month p ON 1=1;
    `;
    const cc26Query = `
      SELECT 
        COUNT(*) as show_count,
        ROUND(SUM(s.total_run_time) / 3600.0, 1) as total_hours
      FROM cc_26.schedule sch
      JOIN cc_26.shows s ON sch.show = s.id
      WHERE run_date_time >= NOW() - INTERVAL '30 days'
      AND run_date_time <= NOW()
    `;
    const damsQuery = `
      SELECT 
        asset_count,
        all_time_download
      FROM dams.stats 
      WHERE report_date = (
        SELECT MAX(report_date) 
        FROM dams.stats
      )
      LIMIT 1
    `;
    const websiteQuery = `
      SELECT SUM("totalUsers") as total_visitors
      FROM ga4.website_overview
      WHERE date >= TO_CHAR(NOW() - INTERVAL '30 days', 'YYYYMMDD')
        AND date <= TO_CHAR(NOW(), 'YYYYMMDD')
    `;

    const [fbResult, fbHistoryResult, igResult, igHistoryResult, ytResult, ytHistoryResult, ndResult, ndHistoryResult, cc26Result, damsResult, websiteResult] = await Promise.all([
      pool.query(fbQuery),
      pool.query(fbHistoryQuery),
      pool.query(igQuery),
      pool.query(igHistoryQuery),
      pool.query(ytQuery),
      pool.query(ytHistoryQuery),
      pool.query(ndQuery),
      pool.query(ndHistoryQuery),
      pool.query(cc26Query),
      pool.query(damsQuery),
      pool.query(websiteQuery),
    ]);

    const currentFbFollowers = fbResult.rows[0]?.followers_count || 0;
    const pastFbFollowers = fbHistoryResult.rows[0]?.followers_count || currentFbFollowers;
    const fbChange = pastFbFollowers > 0 
      ? Number((((currentFbFollowers - pastFbFollowers) / pastFbFollowers) * 100).toFixed(1))
      : 0;
    const fbDifference = currentFbFollowers - pastFbFollowers;

    const currentIgFollowers = igResult.rows[0]?.followers_count || 0;
    const pastIgFollowers = igHistoryResult.rows[0]?.followers_count || currentIgFollowers;
    const igChange = pastIgFollowers > 0 
      ? Number((((currentIgFollowers - pastIgFollowers) / pastIgFollowers) * 100).toFixed(1))
      : 0;
    const igDifference = currentIgFollowers - pastIgFollowers;

    const currentYtSubscribers = ytResult.rows[0]?.subscriber_count || 0;
    const pastYtSubscribers = ytHistoryResult.rows[0]?.subscriber_count || currentYtSubscribers;
    const ytChange = pastYtSubscribers > 0 
      ? Number((((currentYtSubscribers - pastYtSubscribers) / pastYtSubscribers) * 100).toFixed(1))
      : 0;
    const ytDifference = currentYtSubscribers - pastYtSubscribers;

    const currentNdNeighbors = ndResult.rows[0]?.verified_neighbors_count || 0;
    const pastNdNeighbors = ndHistoryResult.rows[0]?.previous_count || currentNdNeighbors;
    const ndChange = pastNdNeighbors > 0 
      ? Number((((currentNdNeighbors - pastNdNeighbors) / pastNdNeighbors) * 100).toFixed(1))
      : 0;
    const ndDifference = currentNdNeighbors - pastNdNeighbors;

    return NextResponse.json({
      facebook: currentFbFollowers,
      facebookChange: fbChange,
      facebookDifference: fbDifference,
      instagram: currentIgFollowers,
      instagramChange: igChange,
      instagramDifference: igDifference,
      youtube: currentYtSubscribers,
      youtubeChange: ytChange,
      youtubeDifference: ytDifference,
      nextdoor: currentNdNeighbors,
      nextdoorChange: ndChange,
      nextdoorDifference: ndDifference,
      cablecast26: {
        showCount: cc26Result.rows[0]?.show_count || 0,
        totalHours: cc26Result.rows[0]?.total_hours || 0
      },
      dams: {
        assetCount: damsResult.rows[0]?.asset_count || 0,
        downloads: damsResult.rows[0]?.all_time_download || 0
      },
      website: {
        visitors: websiteResult.rows[0]?.total_visitors || 0
      }
    });
  } catch (error) {
    console.error('Error fetching social metrics:', error);
    return NextResponse.json({ error: 'Failed to fetch social metrics' }, { status: 500 });
  }
}







