"use client";

import { useState, useEffect } from 'react';
import AddLinkForm from './AddLinkForm';
import CategorySection from './CategorySection';
import AddCategoryForm from './AddCategoryForm';

interface Link {
  id: number;
  title: string;
  url: string;
  category_id: number;
  category_name: string;
}

interface Category {
  id: number;
  name: string;
}

export default function LinksManager() {
  const [links, setLinks] = useState<Link[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isAddingLink, setIsAddingLink] = useState(false);
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);

  useEffect(() => {
    fetchLinks();
    fetchCategories();
  }, []);

  const fetchLinks = async () => {
    try {
      const response = await fetch('/api/links');
      const data = await response.json();
      setLinks(data.links || []);
    } catch (error) {
      console.error('Error fetching links:', error);
      setLinks([]);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      const data = await response.json();
      setCategories(data.categories || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
      setCategories([]);
    }
  };

  const handleLinkAdded = async () => {
    await fetchLinks();
    setIsAddingLink(false);
  };

  const handleCategoryAdded = async () => {
    await fetchCategories();
    setIsAddingCategory(false);
  };

  const handleCategoryEdit = async () => {
    // First fetch both data
    await Promise.all([
      fetchCategories(),
      fetchLinks() // We need this because links contain category_name
    ]);
    // Then close the edit form
    setEditingCategory(null);
  };

  const handleLinkDeleted = async (linkId: number) => {
    try {
      await fetch(`/api/links/${linkId}`, { method: 'DELETE' });
      await fetchLinks();
    } catch (error) {
      console.error('Error deleting link:', error);
    }
  };

  const groupedLinks = (links || []).reduce((acc, link) => {
    if (!acc[link.category_name]) {
      acc[link.category_name] = [];
    }
    acc[link.category_name].push(link);
    return acc;
  }, {} as Record<string, Link[]>);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="space-x-4">
          <button
            onClick={() => setIsAddingLink(true)}
            className="bg-[#365F9F] text-white px-4 py-2 rounded hover:bg-[#2b4c80]"
          >
            Add New Link
          </button>
          <button
            onClick={() => setIsAddingCategory(true)}
            className="bg-[#AAB058] text-white px-4 py-2 rounded hover:bg-[#899247]"
          >
            Add New Category
          </button>
        </div>
      </div>

      {isAddingLink && (
        <AddLinkForm
          categories={categories}
          onAdd={handleLinkAdded}
          onCancel={() => setIsAddingLink(false)}
        />
      )}

      {(isAddingCategory || editingCategory) && (
        <AddCategoryForm
          categories={categories}
          onAdd={editingCategory ? handleCategoryEdit : handleCategoryAdded}
          onCancel={() => {
            setIsAddingCategory(false);
            setEditingCategory(null);
          }}
          initialData={editingCategory || undefined}
        />
      )}

      <div className="space-y-8">
        {Object.entries(groupedLinks)
          .sort(([a], [b]) => a.localeCompare(b))
          .map(([categoryName, categoryLinks]) => (
            <CategorySection
              key={categoryName}
              categoryName={categoryName}
              links={categoryLinks}
              onDelete={handleLinkDeleted}
              onEdit={fetchLinks}
              categories={categories}
              onEditCategory={(category) => setEditingCategory(category)}
            />
          ))}
      </div>
    </div>
  );
}






