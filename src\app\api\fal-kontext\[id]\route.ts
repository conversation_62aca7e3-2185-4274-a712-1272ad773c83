import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import pool from '@/lib/db';

// PATCH - Update a Kontext generation (for toggling favorite)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID format' },
        { status: 400 }
      );
    }

    const { favorite } = await request.json();
    if (typeof favorite !== 'boolean') {
      return NextResponse.json(
        { error: 'Invalid favorite value' },
        { status: 400 }
      );
    }

    const result = await pool.query(
      `UPDATE fal_kontext.generations
       SET favorite = $1
       WHERE id = $2
       RETURNING id, prompt, original_image_url, result_image_url, created_at, favorite`,
      [favorite, id]
    );

    if (result.rowCount === 0) {
      return NextResponse.json(
        { error: 'Generation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ generation: result.rows[0] });
  } catch (error) {
    console.error('Error updating Kontext generation:', error);
    return NextResponse.json(
      { error: 'Failed to update generation' },
      { status: 500 }
    );
  }
}

// DELETE - Delete a Kontext generation
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID format' },
        { status: 400 }
      );
    }

    const result = await pool.query(
      `DELETE FROM fal_kontext.generations
       WHERE id = $1
       RETURNING id`,
      [id]
    );

    if (result.rowCount === 0) {
      return NextResponse.json(
        { error: 'Generation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting Kontext generation:', error);
    return NextResponse.json(
      { error: 'Failed to delete generation' },
      { status: 500 }
    );
  }
}