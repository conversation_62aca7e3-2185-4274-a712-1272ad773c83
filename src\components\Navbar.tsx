"use client";

import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  
  return (
    <nav className="bg-[#365F9F] text-white py-2 shadow-md relative">
      <div className="flex justify-between items-center w-full">
        <div className="absolute left-2">
          <Image
            src="/images/coflogo.png"
            alt="City of Fairfield Logo"
            width={200}
            height={50}
            priority
            style={{ objectFit: 'contain' }}
          />
        </div>
        
        {/* Desktop navigation - centered and pushed right to make room for logo */}
        <div className="hidden md:flex items-center ml-auto space-x-6 pr-4">
          <Link href="/" className="hover:text-[#FBA541] transition-colors">
            Dashboard
          </Link>
          <Link href="/analytics" className="hover:text-[#FBA541] transition-colors">
            Analytics
          </Link>
          
          {isLoggedIn ? (
            <button 
              onClick={() => setIsLoggedIn(false)}
              className="bg-[#276A30] hover:bg-[#1E5023] px-4 py-2 rounded-md transition-colors"
            >
              Logout
            </button>
          ) : (
            <button 
              onClick={() => setIsLoggedIn(true)}
              className="bg-[#FBA541] hover:bg-[#E09438] px-4 py-2 rounded-md transition-colors"
            >
              Login
            </button>
          )}
        </div>
        
        {/* Mobile menu button */}
        <button 
          className="md:hidden ml-auto pr-4"
          onClick={() => setIsOpen(!isOpen)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-6 h-6">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={isOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"} />
          </svg>
        </button>
      </div>
      
      {/* Mobile navigation */}
      {isOpen && (
        <div className="md:hidden mt-2 pb-4 px-4">
          <div className="flex flex-col space-y-2 pt-12"> {/* Added top padding to avoid overlapping with logo */}
            <Link 
              href="/" 
              className="hover:bg-[#4A74B4] py-2 px-4 rounded-md transition-colors"
              onClick={() => setIsOpen(false)}
            >
              Dashboard
            </Link>
            <Link 
              href="/analytics" 
              className="hover:bg-[#4A74B4] py-2 px-4 rounded-md transition-colors"
              onClick={() => setIsOpen(false)}
            >
              Analytics
            </Link>
            
            {isLoggedIn ? (
              <button 
                onClick={() => {
                  setIsLoggedIn(false);
                  setIsOpen(false);
                }}
                className="bg-[#276A30] hover:bg-[#1E5023] px-4 py-2 rounded-md transition-colors text-left"
              >
                Logout
              </button>
            ) : (
              <button 
                onClick={() => {
                  setIsLoggedIn(true);
                  setIsOpen(false);
                }}
                className="bg-[#FBA541] hover:bg-[#E09438] px-4 py-2 rounded-md transition-colors text-left"
              >
                Login
              </button>
            )}
          </div>
        </div>
      )}
    </nav>
  );
}

