# Changelog

## [Unreleased]

### Added
- Initial project setup with Next.js 15.2
- TypeScript configuration and support
- Tailwind CSS integration
- ESLint configuration
- Basic project structure and layouts
- Database connection setup with PostgreSQL
- Social media metrics API endpoint
- Dashboard layout component
- Social metrics component with real-time data
- Cablecast Channel 26 metrics integration
  - Show count tracking
  - Total runtime calculation in hours
- Campaign management database schema
  - Created campaigns table for tracking marketing campaigns
  - Created posts table for associated social media content
- Environment variable configuration
- Basic documentation in README.md

### Changed
- Updated project dependencies to latest stable versions
- Modified social metrics display to include Cablecast data
- Enhanced metric cards to support additional display options

### Technical Details

#### Database Schema Updates
1. Created campaign schema tables:
   - campaigns (id, title, start_date, end_date, highlighted, web_url_path)
   - posts (id, campaign_id, title, description, yt_id, fb_id, ig_id)

#### API Endpoints
1. /api/social-metrics
   - Added Cablecast show count and runtime calculations
   - Integrated with multiple social media platforms

#### Component Updates
1. SocialMetrics.tsx
   - Added support for rightSubValue in MetricCard
   - Enhanced display of Cablecast metrics

#### Configuration
1. Updated database connection configuration
2. Enhanced TypeScript configuration
3. Modified ESLint rules for better code quality

### Known Issues
- None reported

### Upcoming Features
- Campaign management interface
- Enhanced analytics dashboard
- Social media post scheduling
- Digital asset management system integration.