"use client";

import { useState } from 'react';

interface Category {
  id: number;
  name: string;
}

interface AddCategoryFormProps {
  onAdd: () => void;
  onCancel: () => void;
  categories: Category[];
  initialData?: Category;
}

export default function AddCategoryForm({ onAdd, onCancel, categories, initialData }: AddCategoryFormProps) {
  const [name, setName] = useState(initialData?.name || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsSubmitting(true);

    try {
      const endpoint = initialData?.id 
        ? `/api/categories/${initialData.id}`
        : '/api/categories';
      
      const method = initialData?.id ? 'PUT' : 'POST';

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to save category');
      }

      await onAdd();
    } catch (error) {
      console.error('Error saving category:', error);
      setError(error instanceof Error ? error.message : 'Failed to save category');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="bg-gray-50 p-6 rounded-lg space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {initialData ? 'Edit Category Name' : 'New Category Name'}
        </label>
        <input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          className="mt-1 block w-full px-4 py-3 text-gray-900 bg-white border border-gray-300 rounded-lg shadow-sm focus:border-[#365F9F] focus:ring-[#365F9F] text-base"
          required
          disabled={isSubmitting}
        />
        {error && (
          <p className="mt-2 text-sm text-red-600">{error}</p>
        )}
      </div>

      {!initialData && (
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Existing Categories:</label>
          <div className="bg-white p-3 rounded-lg border border-gray-200 max-h-40 overflow-y-auto">
            {categories.map((category) => (
              <div key={category.id} className="py-1 px-2 text-gray-600">
                {category.name}
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-6 py-3 text-base font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
          disabled={isSubmitting}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-6 py-3 text-base font-medium text-white bg-[#365F9F] rounded-lg hover:bg-[#2b4c80] disabled:opacity-50"
          disabled={isSubmitting}
        >
          {isSubmitting 
            ? 'Saving...' 
            : initialData 
              ? 'Update Category' 
              : 'Add Category'
          }
        </button>
      </div>
    </form>
  );
}


